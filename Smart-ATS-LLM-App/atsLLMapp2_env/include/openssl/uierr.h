/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_UIERR_H
# define OPENSSL_UIERR_H
# pragma once

# include <openssl/opensslconf.h>
# include <openssl/symhacks.h>
# include <openssl/cryptoerr_legacy.h>



/*
 * UI reason codes.
 */
# define UI_R_COMMON_OK_AND_CANCEL_CHARACTERS             104
# define UI_R_INDEX_TOO_LARGE                             102
# define UI_R_INDEX_TOO_SMALL                             103
# define UI_R_NO_RESULT_BUFFER                            105
# define UI_R_PROCESSING_ERROR                            107
# define UI_R_RESULT_TOO_LARGE                            100
# define UI_R_RESULT_TOO_SMALL                            101
# define UI_R_SYSASSIGN_ERROR                             109
# define UI_R_SYSDASSGN_ERROR                             110
# define UI_R_SYSQIOW_ERROR                               111
# define UI_R_UNKNOWN_CONTROL_COMMAND                     106
# define UI_R_UNKNOWN_TTYGET_ERRNO_VALUE                  108
# define UI_R_USER_DATA_DUPLICATION_UNSUPPORTED           112

#endif
