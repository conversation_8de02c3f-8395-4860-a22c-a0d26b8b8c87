==> 2024-10-17 11:36:12 <==
# cmd: /opt/anaconda3/bin/conda create -p atsLLMapp2_env python==3.12 -y
# conda version: 24.9.1
+conda-forge/noarch::pip-24.2-pyh8b19718_1
+conda-forge/noarch::setuptools-75.1.0-pyhd8ed1ab_0
+conda-forge/noarch::tzdata-2024b-hc8b5060_0
+conda-forge/noarch::wheel-0.44.0-pyhd8ed1ab_0
+conda-forge/osx-64::bzip2-1.0.8-hfdf4475_7
+conda-forge/osx-64::ca-certificates-2024.8.30-h8857fd0_0
+conda-forge/osx-64::libexpat-2.6.3-hac325c4_0
+conda-forge/osx-64::libffi-3.4.2-h0d85af4_5
+conda-forge/osx-64::libsqlite-3.46.1-h4b8f8c9_0
+conda-forge/osx-64::libzlib-1.3.1-hd23fc13_2
+conda-forge/osx-64::ncurses-6.5-hf036a51_1
+conda-forge/osx-64::openssl-3.3.2-hd23fc13_0
+conda-forge/osx-64::python-3.12.0-h30d4d87_0_cpython
+conda-forge/osx-64::readline-8.2-h9e318b2_1
+conda-forge/osx-64::tk-8.6.13-h1abcd95_1
+conda-forge/osx-64::xz-5.2.6-h775f41a_0
# update specs: ['python==3.12']
