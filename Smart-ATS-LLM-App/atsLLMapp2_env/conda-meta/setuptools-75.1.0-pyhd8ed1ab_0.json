{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.8"], "extracted_package_dir": "/opt/anaconda3/pkgs/setuptools-75.1.0-pyhd8ed1ab_0", "files": ["lib/python3.12/site-packages/_distutils_hack/__init__.py", "lib/python3.12/site-packages/_distutils_hack/override.py", "lib/python3.12/site-packages/distutils-precedence.pth", "lib/python3.12/site-packages/pkg_resources/__init__.py", "lib/python3.12/site-packages/pkg_resources/api_tests.txt", "lib/python3.12/site-packages/pkg_resources/py.typed", "lib/python3.12/site-packages/pkg_resources/tests/__init__.py", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "lib/python3.12/site-packages/pkg_resources/tests/test_find_distributions.py", "lib/python3.12/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "lib/python3.12/site-packages/pkg_resources/tests/test_markers.py", "lib/python3.12/site-packages/pkg_resources/tests/test_pkg_resources.py", "lib/python3.12/site-packages/pkg_resources/tests/test_resources.py", "lib/python3.12/site-packages/pkg_resources/tests/test_working_set.py", "lib/python3.12/site-packages/setuptools-75.1.0-py3.12.egg-info/PKG-INFO", "lib/python3.12/site-packages/setuptools-75.1.0-py3.12.egg-info/SOURCES.txt", "lib/python3.12/site-packages/setuptools-75.1.0-py3.12.egg-info/dependency_links.txt", "lib/python3.12/site-packages/setuptools-75.1.0-py3.12.egg-info/entry_points.txt", "lib/python3.12/site-packages/setuptools-75.1.0-py3.12.egg-info/requires.txt", "lib/python3.12/site-packages/setuptools-75.1.0-py3.12.egg-info/top_level.txt", "lib/python3.12/site-packages/setuptools/__init__.py", "lib/python3.12/site-packages/setuptools/_core_metadata.py", "lib/python3.12/site-packages/setuptools/_distutils/__init__.py", "lib/python3.12/site-packages/setuptools/_distutils/_log.py", "lib/python3.12/site-packages/setuptools/_distutils/_macos_compat.py", "lib/python3.12/site-packages/setuptools/_distutils/_modified.py", "lib/python3.12/site-packages/setuptools/_distutils/_msvccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/archive_util.py", "lib/python3.12/site-packages/setuptools/_distutils/ccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/cmd.py", "lib/python3.12/site-packages/setuptools/_distutils/command/__init__.py", "lib/python3.12/site-packages/setuptools/_distutils/command/_framework_compat.py", "lib/python3.12/site-packages/setuptools/_distutils/command/bdist.py", "lib/python3.12/site-packages/setuptools/_distutils/command/bdist_dumb.py", "lib/python3.12/site-packages/setuptools/_distutils/command/bdist_rpm.py", "lib/python3.12/site-packages/setuptools/_distutils/command/build.py", "lib/python3.12/site-packages/setuptools/_distutils/command/build_clib.py", "lib/python3.12/site-packages/setuptools/_distutils/command/build_ext.py", "lib/python3.12/site-packages/setuptools/_distutils/command/build_py.py", "lib/python3.12/site-packages/setuptools/_distutils/command/build_scripts.py", "lib/python3.12/site-packages/setuptools/_distutils/command/check.py", "lib/python3.12/site-packages/setuptools/_distutils/command/clean.py", "lib/python3.12/site-packages/setuptools/_distutils/command/config.py", "lib/python3.12/site-packages/setuptools/_distutils/command/install.py", "lib/python3.12/site-packages/setuptools/_distutils/command/install_data.py", "lib/python3.12/site-packages/setuptools/_distutils/command/install_egg_info.py", "lib/python3.12/site-packages/setuptools/_distutils/command/install_headers.py", "lib/python3.12/site-packages/setuptools/_distutils/command/install_lib.py", "lib/python3.12/site-packages/setuptools/_distutils/command/install_scripts.py", "lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py", "lib/python3.12/site-packages/setuptools/_distutils/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_distutils/compat/py38.py", "lib/python3.12/site-packages/setuptools/_distutils/compat/py39.py", "lib/python3.12/site-packages/setuptools/_distutils/core.py", "lib/python3.12/site-packages/setuptools/_distutils/cygwinccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/debug.py", "lib/python3.12/site-packages/setuptools/_distutils/dep_util.py", "lib/python3.12/site-packages/setuptools/_distutils/dir_util.py", "lib/python3.12/site-packages/setuptools/_distutils/dist.py", "lib/python3.12/site-packages/setuptools/_distutils/errors.py", "lib/python3.12/site-packages/setuptools/_distutils/extension.py", "lib/python3.12/site-packages/setuptools/_distutils/fancy_getopt.py", "lib/python3.12/site-packages/setuptools/_distutils/file_util.py", "lib/python3.12/site-packages/setuptools/_distutils/filelist.py", "lib/python3.12/site-packages/setuptools/_distutils/log.py", "lib/python3.12/site-packages/setuptools/_distutils/spawn.py", "lib/python3.12/site-packages/setuptools/_distutils/sysconfig.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/__init__.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/compat/py38.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/support.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_archive_util.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_bdist.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_build.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_build_clib.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_build_ext.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_build_py.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_ccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_check.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_clean.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_cmd.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_core.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_cygwinccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_dir_util.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_dist.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_extension.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_file_util.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_filelist.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_install.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_install_data.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_install_headers.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_install_lib.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_log.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_mingwccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_modified.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_msvccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_sdist.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_spawn.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_text_file.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_unixccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_util.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_version.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "lib/python3.12/site-packages/setuptools/_distutils/tests/unix_compat.py", "lib/python3.12/site-packages/setuptools/_distutils/text_file.py", "lib/python3.12/site-packages/setuptools/_distutils/unixccompiler.py", "lib/python3.12/site-packages/setuptools/_distutils/util.py", "lib/python3.12/site-packages/setuptools/_distutils/version.py", "lib/python3.12/site-packages/setuptools/_distutils/versionpredicate.py", "lib/python3.12/site-packages/setuptools/_distutils/zosccompiler.py", "lib/python3.12/site-packages/setuptools/_entry_points.py", "lib/python3.12/site-packages/setuptools/_imp.py", "lib/python3.12/site-packages/setuptools/_importlib.py", "lib/python3.12/site-packages/setuptools/_itertools.py", "lib/python3.12/site-packages/setuptools/_normalization.py", "lib/python3.12/site-packages/setuptools/_path.py", "lib/python3.12/site-packages/setuptools/_reqs.py", "lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/autoasync.py", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/autocommand.py", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/automain.py", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/autoparse.py", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/errors.py", "lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/_adapters.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/_common.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/_itertools.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/abc.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/py38.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/py39.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/functional.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/future/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/future/adapters.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/readers.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/simple.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/_path.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/py312.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/py39.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/binary.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/binary.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-16.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-8.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/resource1.txt", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/subdirectory/subsubdir/resource.txt", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/resource2.txt", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/binary.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/subdirectory/binary.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-16.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-8.file", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_compatibilty_files.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_contents.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_custom.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_files.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_functional.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_open.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_path.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_read.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_reader.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/test_resource.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/util.py", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/zip.py", "lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/inflect/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/py38.py", "lib/python3.12/site-packages/setuptools/_vendor/inflect/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/context.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/more.py", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/more.pyi", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/recipes.py", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.APACHE", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.BSD", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/packaging-24.1.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/_elffile.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/_manylinux.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/_musllinux.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/_parser.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/_structures.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/markers.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/metadata.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/packaging/requirements.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/specifiers.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/tags.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/utils.py", "lib/python3.12/site-packages/setuptools/_vendor/packaging/version.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__main__.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/android.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/api.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/macos.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/unix.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/version.py", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/windows.py", "lib/python3.12/site-packages/setuptools/_vendor/ruff.toml", "lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/tomli/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/tomli/_parser.py", "lib/python3.12/site-packages/setuptools/_vendor/tomli/_re.py", "lib/python3.12/site-packages/setuptools/_vendor/tomli/_types.py", "lib/python3.12/site-packages/setuptools/_vendor/tomli/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "lib/python3.12/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_checkers.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_config.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_decorators.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_functions.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_importhook.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_memo.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_suppression.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_transformer.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/_utils.py", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/py.typed", "lib/python3.12/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/typing_extensions.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt", "lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__main__.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/convert.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/pack.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/tags.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/metadata.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/util.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "lib/python3.12/site-packages/setuptools/_vendor/wheel/wheelfile.py", "lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "lib/python3.12/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "lib/python3.12/site-packages/setuptools/_vendor/zipp/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/py310.py", "lib/python3.12/site-packages/setuptools/_vendor/zipp/glob.py", "lib/python3.12/site-packages/setuptools/archive_util.py", "lib/python3.12/site-packages/setuptools/build_meta.py", "lib/python3.12/site-packages/setuptools/cli-32.exe", "lib/python3.12/site-packages/setuptools/cli-64.exe", "lib/python3.12/site-packages/setuptools/cli-arm64.exe", "lib/python3.12/site-packages/setuptools/cli.exe", "lib/python3.12/site-packages/setuptools/command/__init__.py", "lib/python3.12/site-packages/setuptools/command/_requirestxt.py", "lib/python3.12/site-packages/setuptools/command/alias.py", "lib/python3.12/site-packages/setuptools/command/bdist_egg.py", "lib/python3.12/site-packages/setuptools/command/bdist_rpm.py", "lib/python3.12/site-packages/setuptools/command/bdist_wheel.py", "lib/python3.12/site-packages/setuptools/command/build.py", "lib/python3.12/site-packages/setuptools/command/build_clib.py", "lib/python3.12/site-packages/setuptools/command/build_ext.py", "lib/python3.12/site-packages/setuptools/command/build_py.py", "lib/python3.12/site-packages/setuptools/command/develop.py", "lib/python3.12/site-packages/setuptools/command/dist_info.py", "lib/python3.12/site-packages/setuptools/command/easy_install.py", "lib/python3.12/site-packages/setuptools/command/editable_wheel.py", "lib/python3.12/site-packages/setuptools/command/egg_info.py", "lib/python3.12/site-packages/setuptools/command/install.py", "lib/python3.12/site-packages/setuptools/command/install_egg_info.py", "lib/python3.12/site-packages/setuptools/command/install_lib.py", "lib/python3.12/site-packages/setuptools/command/install_scripts.py", "lib/python3.12/site-packages/setuptools/command/launcher manifest.xml", "lib/python3.12/site-packages/setuptools/command/rotate.py", "lib/python3.12/site-packages/setuptools/command/saveopts.py", "lib/python3.12/site-packages/setuptools/command/sdist.py", "lib/python3.12/site-packages/setuptools/command/setopt.py", "lib/python3.12/site-packages/setuptools/command/test.py", "lib/python3.12/site-packages/setuptools/compat/__init__.py", "lib/python3.12/site-packages/setuptools/compat/py310.py", "lib/python3.12/site-packages/setuptools/compat/py311.py", "lib/python3.12/site-packages/setuptools/compat/py312.py", "lib/python3.12/site-packages/setuptools/compat/py39.py", "lib/python3.12/site-packages/setuptools/config/NOTICE", "lib/python3.12/site-packages/setuptools/config/__init__.py", "lib/python3.12/site-packages/setuptools/config/_apply_pyprojecttoml.py", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/NOTICE", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__init__.py", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/formats.py", "lib/python3.12/site-packages/setuptools/config/distutils.schema.json", "lib/python3.12/site-packages/setuptools/config/expand.py", "lib/python3.12/site-packages/setuptools/config/pyprojecttoml.py", "lib/python3.12/site-packages/setuptools/config/setupcfg.py", "lib/python3.12/site-packages/setuptools/config/setuptools.schema.json", "lib/python3.12/site-packages/setuptools/depends.py", "lib/python3.12/site-packages/setuptools/discovery.py", "lib/python3.12/site-packages/setuptools/dist.py", "lib/python3.12/site-packages/setuptools/errors.py", "lib/python3.12/site-packages/setuptools/extension.py", "lib/python3.12/site-packages/setuptools/glob.py", "lib/python3.12/site-packages/setuptools/gui-32.exe", "lib/python3.12/site-packages/setuptools/gui-64.exe", "lib/python3.12/site-packages/setuptools/gui-arm64.exe", "lib/python3.12/site-packages/setuptools/gui.exe", "lib/python3.12/site-packages/setuptools/installer.py", "lib/python3.12/site-packages/setuptools/launch.py", "lib/python3.12/site-packages/setuptools/logging.py", "lib/python3.12/site-packages/setuptools/modified.py", "lib/python3.12/site-packages/setuptools/monkey.py", "lib/python3.12/site-packages/setuptools/msvc.py", "lib/python3.12/site-packages/setuptools/namespaces.py", "lib/python3.12/site-packages/setuptools/package_index.py", "lib/python3.12/site-packages/setuptools/sandbox.py", "lib/python3.12/site-packages/setuptools/script (dev).tmpl", "lib/python3.12/site-packages/setuptools/script.tmpl", "lib/python3.12/site-packages/setuptools/tests/__init__.py", "lib/python3.12/site-packages/setuptools/tests/compat/__init__.py", "lib/python3.12/site-packages/setuptools/tests/compat/py39.py", "lib/python3.12/site-packages/setuptools/tests/config/__init__.py", "lib/python3.12/site-packages/setuptools/tests/config/downloads/__init__.py", "lib/python3.12/site-packages/setuptools/tests/config/downloads/preload.py", "lib/python3.12/site-packages/setuptools/tests/config/setupcfg_examples.txt", "lib/python3.12/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "lib/python3.12/site-packages/setuptools/tests/config/test_expand.py", "lib/python3.12/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "lib/python3.12/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "lib/python3.12/site-packages/setuptools/tests/config/test_setupcfg.py", "lib/python3.12/site-packages/setuptools/tests/contexts.py", "lib/python3.12/site-packages/setuptools/tests/environment.py", "lib/python3.12/site-packages/setuptools/tests/fixtures.py", "lib/python3.12/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "lib/python3.12/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "lib/python3.12/site-packages/setuptools/tests/integration/__init__.py", "lib/python3.12/site-packages/setuptools/tests/integration/helpers.py", "lib/python3.12/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "lib/python3.12/site-packages/setuptools/tests/mod_with_constant.py", "lib/python3.12/site-packages/setuptools/tests/namespaces.py", "lib/python3.12/site-packages/setuptools/tests/script-with-bom.py", "lib/python3.12/site-packages/setuptools/tests/server.py", "lib/python3.12/site-packages/setuptools/tests/test_archive_util.py", "lib/python3.12/site-packages/setuptools/tests/test_bdist_deprecations.py", "lib/python3.12/site-packages/setuptools/tests/test_bdist_egg.py", "lib/python3.12/site-packages/setuptools/tests/test_bdist_wheel.py", "lib/python3.12/site-packages/setuptools/tests/test_build.py", "lib/python3.12/site-packages/setuptools/tests/test_build_clib.py", "lib/python3.12/site-packages/setuptools/tests/test_build_ext.py", "lib/python3.12/site-packages/setuptools/tests/test_build_meta.py", "lib/python3.12/site-packages/setuptools/tests/test_build_py.py", "lib/python3.12/site-packages/setuptools/tests/test_config_discovery.py", "lib/python3.12/site-packages/setuptools/tests/test_core_metadata.py", "lib/python3.12/site-packages/setuptools/tests/test_depends.py", "lib/python3.12/site-packages/setuptools/tests/test_develop.py", "lib/python3.12/site-packages/setuptools/tests/test_dist.py", "lib/python3.12/site-packages/setuptools/tests/test_dist_info.py", "lib/python3.12/site-packages/setuptools/tests/test_distutils_adoption.py", "lib/python3.12/site-packages/setuptools/tests/test_easy_install.py", "lib/python3.12/site-packages/setuptools/tests/test_editable_install.py", "lib/python3.12/site-packages/setuptools/tests/test_egg_info.py", "lib/python3.12/site-packages/setuptools/tests/test_extern.py", "lib/python3.12/site-packages/setuptools/tests/test_find_packages.py", "lib/python3.12/site-packages/setuptools/tests/test_find_py_modules.py", "lib/python3.12/site-packages/setuptools/tests/test_glob.py", "lib/python3.12/site-packages/setuptools/tests/test_install_scripts.py", "lib/python3.12/site-packages/setuptools/tests/test_logging.py", "lib/python3.12/site-packages/setuptools/tests/test_manifest.py", "lib/python3.12/site-packages/setuptools/tests/test_namespaces.py", "lib/python3.12/site-packages/setuptools/tests/test_packageindex.py", "lib/python3.12/site-packages/setuptools/tests/test_sandbox.py", "lib/python3.12/site-packages/setuptools/tests/test_sdist.py", "lib/python3.12/site-packages/setuptools/tests/test_setopt.py", "lib/python3.12/site-packages/setuptools/tests/test_setuptools.py", "lib/python3.12/site-packages/setuptools/tests/test_unicode_utils.py", "lib/python3.12/site-packages/setuptools/tests/test_virtualenv.py", "lib/python3.12/site-packages/setuptools/tests/test_warnings.py", "lib/python3.12/site-packages/setuptools/tests/test_wheel.py", "lib/python3.12/site-packages/setuptools/tests/test_windows_wrappers.py", "lib/python3.12/site-packages/setuptools/tests/text.py", "lib/python3.12/site-packages/setuptools/tests/textwrap.py", "lib/python3.12/site-packages/setuptools/unicode_utils.py", "lib/python3.12/site-packages/setuptools/version.py", "lib/python3.12/site-packages/setuptools/warnings.py", "lib/python3.12/site-packages/setuptools/wheel.py", "lib/python3.12/site-packages/setuptools/windows_support.py", "lib/python3.12/site-packages/_distutils_hack/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/_distutils_hack/__pycache__/override.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-312.pyc", "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_core_metadata.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_log.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/compat/__pycache__/py38.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/core.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/debug.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/dist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/errors.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/extension.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/log.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/compat/__pycache__/py38.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_ccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_cygwinccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_mingwccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_msvccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_unixccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/version.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_entry_points.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_imp.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_importlib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_itertools.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_normalization.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_path.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/_reqs.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_adapters.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_common.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_itertools.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/abc.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py38.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py39.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/functional.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/adapters.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/readers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/simple.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/_path.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py312.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py39.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_compatibilty_files.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_contents.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_custom.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_files.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_functional.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_open.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_path.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_read.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_reader.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_resource.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/zip.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/archive_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/build_meta.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/alias.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/build.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/build_clib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/build_ext.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/build_py.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/develop.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/dist_info.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/easy_install.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/egg_info.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/install.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/install_lib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/install_scripts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/rotate.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/saveopts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/sdist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/setopt.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/command/__pycache__/test.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/compat/__pycache__/py310.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/compat/__pycache__/py311.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/compat/__pycache__/py312.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/compat/__pycache__/py39.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/__pycache__/expand.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/config/__pycache__/setupcfg.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/depends.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/discovery.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/dist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/errors.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/extension.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/glob.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/installer.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/launch.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/logging.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/modified.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/monkey.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/msvc.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/namespaces.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/package_index.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/sandbox.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/contexts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/environment.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/fixtures.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/namespaces.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/server.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_depends.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_develop.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_dist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_extern.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_glob.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_logging.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/text.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/tests/__pycache__/textwrap.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/unicode_utils.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/version.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/warnings.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/wheel.cpython-312.pyc", "lib/python3.12/site-packages/setuptools/__pycache__/windows_support.cpython-312.pyc"], "fn": "setuptools-75.1.0-pyhd8ed1ab_0.conda", "license": "MIT", "link": {"source": "/opt/anaconda3/pkgs/setuptools-75.1.0-pyhd8ed1ab_0", "type": 1}, "md5": "d5cd48392c67fb6849ba459c2c2b671f", "name": "setuptools", "noarch": "python", "package_tarball_full_path": "/opt/anaconda3/pkgs/setuptools-75.1.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "a325c4f62f92a19daaa558088e4339afd78e14df40c20a7d51c9dc3a714dc237", "sha256_in_prefix": "a325c4f62f92a19daaa558088e4339afd78e14df40c20a7d51c9dc3a714dc237", "size_in_bytes": 6754}, {"_path": "site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "2638ce9e2500e572a5e0de7faed6661eb569d1b696fcba07b0dd223da5f5d224", "sha256_in_prefix": "2638ce9e2500e572a5e0de7faed6661eb569d1b696fcba07b0dd223da5f5d224", "size_in_bytes": 151}, {"_path": "site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "f9debd04eb4cc7e904db6fbc252287243e21c7e75dfbc73cbfc1bf9d3d4772e4", "sha256_in_prefix": "f9debd04eb4cc7e904db6fbc252287243e21c7e75dfbc73cbfc1bf9d3d4772e4", "size_in_bytes": 126236}, {"_path": "site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "9703980093ae8b47bcea2b184fb72af4e24baac07c7971c1a9396b40e6a83c88", "sha256_in_prefix": "9703980093ae8b47bcea2b184fb72af4e24baac07c7971c1a9396b40e6a83c88", "size_in_bytes": 15221}, {"_path": "site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "be3bdb5fd4a40d12dcc483b9fe0dd9b97f998965255a869734776b114cb115db", "sha256_in_prefix": "be3bdb5fd4a40d12dcc483b9fe0dd9b97f998965255a869734776b114cb115db", "size_in_bytes": 31252}, {"_path": "site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "bf795fdcd4ac18637028915963bcb50b9fdca7e7675eca3106e4b17b547093bf", "sha256_in_prefix": "bf795fdcd4ac18637028915963bcb50b9fdca7e7675eca3106e4b17b547093bf", "size_in_bytes": 8531}, {"_path": "site-packages/setuptools-75.1.0-py3.12.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "79d9bccd2ccf10e1ac4d204cec32c033b86889d464cacbf89c8a56c6c88fa5bf", "sha256_in_prefix": "79d9bccd2ccf10e1ac4d204cec32c033b86889d464cacbf89c8a56c6c88fa5bf", "size_in_bytes": 6747}, {"_path": "site-packages/setuptools-75.1.0-py3.12.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "d6c6d4b49f9d02054f3ca153339edb65d8202e2e4190121731beda9eff9a3b69", "sha256_in_prefix": "d6c6d4b49f9d02054f3ca153339edb65d8202e2e4190121731beda9eff9a3b69", "size_in_bytes": 26967}, {"_path": "site-packages/setuptools-75.1.0-py3.12.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/setuptools-75.1.0-py3.12.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "site-packages/setuptools-75.1.0-py3.12.egg-info/requires.txt", "path_type": "hardlink", "sha256": "8a17de53e3a00d8c689f2da11392108c59bf7c3671a01e176e07e1ee21374d3f", "sha256_in_prefix": "8a17de53e3a00d8c689f2da11392108c59bf7c3671a01e176e07e1ee21374d3f", "size_in_bytes": 1309}, {"_path": "site-packages/setuptools-75.1.0-py3.12.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "590952d9d042497dd2ce81fae1dce3735b0923e3aed86646cef6d888b08258c0", "sha256_in_prefix": "590952d9d042497dd2ce81fae1dce3735b0923e3aed86646cef6d888b08258c0", "size_in_bytes": 10389}, {"_path": "site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "ef1794b0ba1c692650960450f35f87551316516157332149a55ca30832605d2d", "sha256_in_prefix": "ef1794b0ba1c692650960450f35f87551316516157332149a55ca30832605d2d", "size_in_bytes": 9795}, {"_path": "site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "259bc850a1e27673bfc9d74e692f68697752ad69f240c89f6ad68092fa6c9c85", "sha256_in_prefix": "259bc850a1e27673bfc9d74e692f68697752ad69f240c89f6ad68092fa6c9c85", "size_in_bytes": 2446}, {"_path": "site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "e045f2facc92015955ba273207a0b6dacf030b4f57e89d9dd677f729f275e391", "sha256_in_prefix": "e045f2facc92015955ba273207a0b6dacf030b4f57e89d9dd677f729f275e391", "size_in_bytes": 21195}, {"_path": "site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "d798c76cb9820dc9d9ef9276b451720a608feb2176696133573fa5bac69ecabe", "sha256_in_prefix": "d798c76cb9820dc9d9ef9276b451720a608feb2176696133573fa5bac69ecabe", "size_in_bytes": 7844}, {"_path": "site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "c6f05081cb16be3b7581ddc61f7471ac1c428484eaf8d2a114929455840f5b3d", "sha256_in_prefix": "c6f05081cb16be3b7581ddc61f7471ac1c428484eaf8d2a114929455840f5b3d", "size_in_bytes": 48873}, {"_path": "site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "b197839d81ee66aca434c3a55f8bacb25c49e23c0f8c05ca26f5d8bb9a3bb67b", "sha256_in_prefix": "b197839d81ee66aca434c3a55f8bacb25c49e23c0f8c05ca26f5d8bb9a3bb67b", "size_in_bytes": 17877}, {"_path": "site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "9002e6ae1113d2944a952a98dc476f17e634cf3fa2254c78fe3a2b1fd31d69d9", "sha256_in_prefix": "9002e6ae1113d2944a952a98dc476f17e634cf3fa2254c78fe3a2b1fd31d69d9", "size_in_bytes": 5346}, {"_path": "site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1a2fddd4dcf897a1b3ff15382d17d1551281ae2db65a834f33bb98c97da4b1d9", "sha256_in_prefix": "1a2fddd4dcf897a1b3ff15382d17d1551281ae2db65a834f33bb98c97da4b1d9", "size_in_bytes": 4582}, {"_path": "site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "0df660bcf9a6dcf4c0777f58ccb790f1f99bc9119a5e8fa79a7533604b5c720d", "sha256_in_prefix": "0df660bcf9a6dcf4c0777f58ccb790f1f99bc9119a5e8fa79a7533604b5c720d", "size_in_bytes": 21686}, {"_path": "site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "fe352a1b719628875d39e31592d3d4b55bf4e61481b7eff4ccbad3072bb7fea0", "sha256_in_prefix": "fe352a1b719628875d39e31592d3d4b55bf4e61481b7eff4ccbad3072bb7fea0", "size_in_bytes": 5729}, {"_path": "site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "a93a6fcfe0dbdb01232a45ff90fc184331540a83f91d9adc6cbbb81c6293274a", "sha256_in_prefix": "a93a6fcfe0dbdb01232a45ff90fc184331540a83f91d9adc6cbbb81c6293274a", "size_in_bytes": 7684}, {"_path": "site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "82ced3577300686e21cac3e4db88546bb33a8d99c9cc5862fe43086f03a760bb", "sha256_in_prefix": "82ced3577300686e21cac3e4db88546bb33a8d99c9cc5862fe43086f03a760bb", "size_in_bytes": 31758}, {"_path": "site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "70d7bcbf0321c8f27680dea3a2df5c398e4eb943d4bd3ea3f5f205702f857229", "sha256_in_prefix": "70d7bcbf0321c8f27680dea3a2df5c398e4eb943d4bd3ea3f5f205702f857229", "size_in_bytes": 16552}, {"_path": "site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "107095c8288ff403c4f57ef84a43968f801d4b98810ae7bb3eaa4b40ff3a7b56", "sha256_in_prefix": "107095c8288ff403c4f57ef84a43968f801d4b98810ae7bb3eaa4b40ff3a7b56", "size_in_bytes": 5534}, {"_path": "site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "38cf7fa584b3eb699a2339772edcebb5343ae7c39943ddec3a5b3ce884e085a2", "sha256_in_prefix": "38cf7fa584b3eb699a2339772edcebb5343ae7c39943ddec3a5b3ce884e085a2", "size_in_bytes": 4897}, {"_path": "site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "aa52ad87be2358b66329ada7c4e6b2ff616e6ba315353ae80296903af6b67707", "sha256_in_prefix": "aa52ad87be2358b66329ada7c4e6b2ff616e6ba315353ae80296903af6b67707", "size_in_bytes": 2595}, {"_path": "site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "14a776bd44953a9d2ba5551eaf86e3e83f78f9fcb1c85f072718ad46564573d7", "sha256_in_prefix": "14a776bd44953a9d2ba5551eaf86e3e83f78f9fcb1c85f072718ad46564573d7", "size_in_bytes": 13008}, {"_path": "site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "4c6fe56d36c58a6da662dd6532030d4c3f6b6ad6a0d0e275182b72b87a5eee8a", "sha256_in_prefix": "4c6fe56d36c58a6da662dd6532030d4c3f6b6ad6a0d0e275182b72b87a5eee8a", "size_in_bytes": 30078}, {"_path": "site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "4eacdaa10f0f223eed4dcdb958a3d0f35699bcffdd4d8638a7daffb6ab5d9a0f", "sha256_in_prefix": "4eacdaa10f0f223eed4dcdb958a3d0f35699bcffdd4d8638a7daffb6ab5d9a0f", "size_in_bytes": 2816}, {"_path": "site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "4b2d9fb81aaf08695056125c52d7866dd1b4282c7604137b8c0cd610c03dae9f", "sha256_in_prefix": "4b2d9fb81aaf08695056125c52d7866dd1b4282c7604137b8c0cd610c03dae9f", "size_in_bytes": 2788}, {"_path": "site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "5a6a70da7a513541983d1d4101b46945a15b0c515ff8a282115cd41b212ecaf6", "sha256_in_prefix": "5a6a70da7a513541983d1d4101b46945a15b0c515ff8a282115cd41b212ecaf6", "size_in_bytes": 1184}, {"_path": "site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "a4a2cd135ae7a9da12a3c6eaf5e7d06d0b90a6b8394c6b30169bca91ad45dc58", "sha256_in_prefix": "a4a2cd135ae7a9da12a3c6eaf5e7d06d0b90a6b8394c6b30169bca91ad45dc58", "size_in_bytes": 8330}, {"_path": "site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "430f0aac2db899c21e244bdc04d28848ca62ef99e94a6ea3cd6e813b303d1bb8", "sha256_in_prefix": "430f0aac2db899c21e244bdc04d28848ca62ef99e94a6ea3cd6e813b303d1bb8", "size_in_bytes": 1937}, {"_path": "site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "e96973cf6ad101cba0d84bbbb8d384f443b76fa23642312572d0a5823c19e63f", "sha256_in_prefix": "e96973cf6ad101cba0d84bbbb8d384f443b76fa23642312572d0a5823c19e63f", "size_in_bytes": 18809}, {"_path": "site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "02131d8b70335fad8a62735d382704b8d5cbb813b186f392407b5d8e2e20f33f", "sha256_in_prefix": "02131d8b70335fad8a62735d382704b8d5cbb813b186f392407b5d8e2e20f33f", "size_in_bytes": 429}, {"_path": "site-packages/setuptools/_distutils/compat/py38.py", "path_type": "hardlink", "sha256": "427211152bc32a240d1a941e6d35ca982ff664bba61f4f23e73f32f3e274e153", "sha256_in_prefix": "427211152bc32a240d1a941e6d35ca982ff664bba61f4f23e73f32f3e274e153", "size_in_bytes": 791}, {"_path": "site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "fcc99978d21b928a56d0b747b47ef0dc748e23f5d3cd5853895f2701edd45b9c", "sha256_in_prefix": "fcc99978d21b928a56d0b747b47ef0dc748e23f5d3cd5853895f2701edd45b9c", "size_in_bytes": 9267}, {"_path": "site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "d4b40ed29f80c0348dccb264fca3c82a9eb67a20e99066787cc32cd8dde8f78c", "sha256_in_prefix": "d4b40ed29f80c0348dccb264fca3c82a9eb67a20e99066787cc32cd8dde8f78c", "size_in_bytes": 11891}, {"_path": "site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "cf0c34b77d667dd5d5ec5d6801916575d4b6e622a581959a34d30b28bf33125c", "sha256_in_prefix": "cf0c34b77d667dd5d5ec5d6801916575d4b6e622a581959a34d30b28bf33125c", "size_in_bytes": 7407}, {"_path": "site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "679d7e17289ba2e2765e2d8a555f4619bd4eb9f43221d496811f1060b2872ee6", "sha256_in_prefix": "679d7e17289ba2e2765e2d8a555f4619bd4eb9f43221d496811f1060b2872ee6", "size_in_bytes": 50553}, {"_path": "site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "6d9ddc2f5629998547258120c3c50cf2f96c2cc2297805ea8ba203495f58aa1c", "sha256_in_prefix": "6d9ddc2f5629998547258120c3c50cf2f96c2cc2297805ea8ba203495f58aa1c", "size_in_bytes": 3325}, {"_path": "site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "d922c9f2fcd8667fc73c400852a13121d276db24b1958a0411e8dbacca010527", "sha256_in_prefix": "d922c9f2fcd8667fc73c400852a13121d276db24b1958a0411e8dbacca010527", "size_in_bytes": 10358}, {"_path": "site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "15f04a8dfcc05cec20d70248543048a1381a96cf7e5cba50762f986d94bcd89c", "sha256_in_prefix": "15f04a8dfcc05cec20d70248543048a1381a96cf7e5cba50762f986d94bcd89c", "size_in_bytes": 17822}, {"_path": "site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "8e93c2760a4437cefa4c549610a3f311b8f8859ac04e964a3d00ce4f81bec874", "sha256_in_prefix": "8e93c2760a4437cefa4c549610a3f311b8f8859ac04e964a3d00ce4f81bec874", "size_in_bytes": 7962}, {"_path": "site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "3e37957e9bef8d8d34b0ec287dde1defd1148e623f73bb9d78f08be9111b6333", "sha256_in_prefix": "3e37957e9bef8d8d34b0ec287dde1defd1148e623f73bb9d78f08be9111b6333", "size_in_bytes": 13654}, {"_path": "site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "bb9b2b15c5680713b0785956b594633bd2fffed45c390bcb1fc0c07a5e646528", "sha256_in_prefix": "bb9b2b15c5680713b0785956b594633bd2fffed45c390bcb1fc0c07a5e646528", "size_in_bytes": 3625}, {"_path": "site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "d2b067ae420c25ce1e93fe6c7da34e3800a947d6d7394f46c4300adca2c7908b", "sha256_in_prefix": "d2b067ae420c25ce1e93fe6c7da34e3800a947d6d7394f46c4300adca2c7908b", "size_in_bytes": 19235}, {"_path": "site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "6c4f6a4622602c99df3cb1c8536d6e63bb9760270763d771f95d31d6f353ffe3", "sha256_in_prefix": "6c4f6a4622602c99df3cb1c8536d6e63bb9760270763d771f95d31d6f353ffe3", "size_in_bytes": 1476}, {"_path": "site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_distutils/tests/compat/py38.py", "path_type": "hardlink", "sha256": "9b24a14132e5f839f202344326b3b8e0774b684a0590a21e45e1e2c9d0a21e0e", "sha256_in_prefix": "9b24a14132e5f839f202344326b3b8e0774b684a0590a21e45e1e2c9d0a21e0e", "size_in_bytes": 1015}, {"_path": "site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "606bfde38d890b82b7321fdfd7163ac71dd71597b174890d763342842ebf15ee", "sha256_in_prefix": "606bfde38d890b82b7321fdfd7163ac71dd71597b174890d763342842ebf15ee", "size_in_bytes": 3933}, {"_path": "site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2dfeba48ef568bf7b1cca82c104e56a553e074d60716bd62bce84a4368310b5a", "sha256_in_prefix": "2dfeba48ef568bf7b1cca82c104e56a553e074d60716bd62bce84a4368310b5a", "size_in_bytes": 1698}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "872c4e7875e9eac0dbf021f1686911e3efbe9cfe67c1b3edafd268009713f585", "sha256_in_prefix": "872c4e7875e9eac0dbf021f1686911e3efbe9cfe67c1b3edafd268009713f585", "size_in_bytes": 19961}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "site-packages/setuptools/_distutils/tests/test_ccompiler.py", "path_type": "hardlink", "sha256": "795cd9644f09308725e0e8f08ae85d48da4d74a92700f3a79487b60e7157fb7f", "sha256_in_prefix": "795cd9644f09308725e0e8f08ae85d48da4d74a92700f3a79487b60e7157fb7f", "size_in_bytes": 2964}, {"_path": "site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "site-packages/setuptools/_distutils/tests/test_cygwinccompiler.py", "path_type": "hardlink", "sha256": "8aac6c0f2d19e594d183133c011ccf5da922b50a1dd95f1a1b9a9eb7f279b538", "sha256_in_prefix": "8aac6c0f2d19e594d183133c011ccf5da922b50a1dd95f1a1b9a9eb7f279b538", "size_in_bytes": 2753}, {"_path": "site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "21d75753bf04a093f11e9979c10d936e5491d1ea2801855c211aaa80003c4cb6", "sha256_in_prefix": "21d75753bf04a093f11e9979c10d936e5491d1ea2801855c211aaa80003c4cb6", "size_in_bytes": 3822}, {"_path": "site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "085941a5b337c091f65a3b20086b135789f96770697cb4a59fa762c41c5eaa93", "sha256_in_prefix": "085941a5b337c091f65a3b20086b135789f96770697cb4a59fa762c41c5eaa93", "size_in_bytes": 18459}, {"_path": "site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "48b2589cb86c85f8f8bbbd90f7513fe639f35696cf963bfaff1a95ef2281d43b", "sha256_in_prefix": "48b2589cb86c85f8f8bbbd90f7513fe639f35696cf963bfaff1a95ef2281d43b", "size_in_bytes": 3094}, {"_path": "site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "af730d0be8370593ca45974718ca926ad33ddc3fdaa284c7067f959b82221035", "sha256_in_prefix": "af730d0be8370593ca45974718ca926ad33ddc3fdaa284c7067f959b82221035", "size_in_bytes": 3502}, {"_path": "site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "0a8f1d0d6082e4afc3530e53e93432801b21fcf4150a83a561039cb25d9a8077", "sha256_in_prefix": "0a8f1d0d6082e4afc3530e53e93432801b21fcf4150a83a561039cb25d9a8077", "size_in_bytes": 10766}, {"_path": "site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "site-packages/setuptools/_distutils/tests/test_mingwccompiler.py", "path_type": "hardlink", "sha256": "98197c5bc4083b6c72e1e3a3e9a0045689b89686f0a4733e1ef154217bbaab47", "sha256_in_prefix": "98197c5bc4083b6c72e1e3a3e9a0045689b89686f0a4733e1ef154217bbaab47", "size_in_bytes": 2202}, {"_path": "site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "site-packages/setuptools/_distutils/tests/test_msvccompiler.py", "path_type": "hardlink", "sha256": "c54adfc82c023b9ec312cc5ca0beacf981b760865196562c2ae6a065b04f149d", "sha256_in_prefix": "c54adfc82c023b9ec312cc5ca0beacf981b760865196562c2ae6a065b04f149d", "size_in_bytes": 4301}, {"_path": "site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "227b1b534f5a795749b63f10cb04449d466e577d9bbe2e3b791987de2590c249", "sha256_in_prefix": "227b1b534f5a795749b63f10cb04449d466e577d9bbe2e3b791987de2590c249", "size_in_bytes": 15058}, {"_path": "site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "6f19384663561660a7c98a4096ab46f157d77e4e53773723579de53b1172ca1e", "sha256_in_prefix": "6f19384663561660a7c98a4096ab46f157d77e4e53773723579de53b1172ca1e", "size_in_bytes": 4613}, {"_path": "site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "887e18f44f141eb7e5dcff954add256e024c947ba842c20ea2bc6bb154509c4f", "sha256_in_prefix": "887e18f44f141eb7e5dcff954add256e024c947ba842c20ea2bc6bb154509c4f", "size_in_bytes": 12010}, {"_path": "site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "site-packages/setuptools/_distutils/tests/test_unixccompiler.py", "path_type": "hardlink", "sha256": "c1c2502615ed914504dc8eb84f20ef337628ec6f5ad2e83f329ec36d92f04f84", "sha256_in_prefix": "c1c2502615ed914504dc8eb84f20ef337628ec6f5ad2e83f329ec36d92f04f84", "size_in_bytes": 11840}, {"_path": "site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6f450c74c441a1fcb2480854627ce2621226741dc2cec53bebde9baca0cfd173", "sha256_in_prefix": "6f450c74c441a1fcb2480854627ce2621226741dc2cec53bebde9baca0cfd173", "size_in_bytes": 2750}, {"_path": "site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "115f210c27ad61e2aae9cfee3dfc06824530f21ba0784a1225c5e9cbf124406a", "sha256_in_prefix": "115f210c27ad61e2aae9cfee3dfc06824530f21ba0784a1225c5e9cbf124406a", "size_in_bytes": 12098}, {"_path": "site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "4091cd71088cb5670e4385b3ba9cc8bf59d0c0110da3e6cd91e542495993e099", "sha256_in_prefix": "4091cd71088cb5670e4385b3ba9cc8bf59d0c0110da3e6cd91e542495993e099", "size_in_bytes": 15437}, {"_path": "site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "c855c29d8a09acbba6f82a2e63c43e6b154c9da952142d50bec48aa51a5801c5", "sha256_in_prefix": "c855c29d8a09acbba6f82a2e63c43e6b154c9da952142d50bec48aa51a5801c5", "size_in_bytes": 17654}, {"_path": "site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "2a56c38c0bc60726d526a443c4d2cd32f64b9795cbd853f47695638337e6d336", "sha256_in_prefix": "2a56c38c0bc60726d526a443c4d2cd32f64b9795cbd853f47695638337e6d336", "size_in_bytes": 12634}, {"_path": "site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "6dbd9d4281a7b2fe0b9a84017e3843b1a3a9b7fa7947bcbfdbc975725b661bde", "sha256_in_prefix": "6dbd9d4281a7b2fe0b9a84017e3843b1a3a9b7fa7947bcbfdbc975725b661bde", "size_in_bytes": 6589}, {"_path": "site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "6f9b04cfe10e24a02932c99bfb224f3e0906a9905e64578bb685a1cac2eb7bed", "sha256_in_prefix": "6f9b04cfe10e24a02932c99bfb224f3e0906a9905e64578bb685a1cac2eb7bed", "size_in_bytes": 2441}, {"_path": "site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "aeb79b8ff62ebd379533e03780524ca7c9518120260f67c0f8d3d756cc73b79c", "sha256_in_prefix": "aeb79b8ff62ebd379533e03780524ca7c9518120260f67c0f8d3d756cc73b79c", "size_in_bytes": 327}, {"_path": "site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "b7e49e5dcd23536c1e418f41037a869514e1cc1343d6860ae47a73835ff9df78", "sha256_in_prefix": "b7e49e5dcd23536c1e418f41037a869514e1cc1343d6860ae47a73835ff9df78", "size_in_bytes": 4536}, {"_path": "site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "723ae776cf9609f0200583c787616c9d9176f78a5c2909c98956bb567a80e3f2", "sha256_in_prefix": "723ae776cf9609f0200583c787616c9d9176f78a5c2909c98956bb567a80e3f2", "size_in_bytes": 2700}, {"_path": "site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "629d699b4ec21eed5052ca1ae827dade6f01e8b459ca89ee8edd421dbfaeced4", "sha256_in_prefix": "629d699b4ec21eed5052ca1ae827dade6f01e8b459ca89ee8edd421dbfaeced4", "size_in_bytes": 1411}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "83878cd8bb8bd0e89971454d0f4ab00c9529136f603afb4edc148f5d36cef459", "sha256_in_prefix": "83878cd8bb8bd0e89971454d0f4ab00c9529136f603afb4edc148f5d36cef459", "size_in_bytes": 3944}, {"_path": "site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "967dd56feea143f1d2c4e98ac1f937c055e61c9aa0425146d55f7ad7c82510fa", "sha256_in_prefix": "967dd56feea143f1d2c4e98ac1f937c055e61c9aa0425146d55f7ad7c82510fa", "size_in_bytes": 7620}, {"_path": "site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/importlib_resources-6.4.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7c72231d4d46670023bdcc9da6652752b4e76ef7625a31b83845592bc6f2d134", "sha256_in_prefix": "7c72231d4d46670023bdcc9da6652752b4e76ef7625a31b83845592bc6f2d134", "size_in_bytes": 20}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/__init__.py", "path_type": "hardlink", "sha256": "bb2a75933611e926b0401b2a9726975df231271e19da633c3239999fcaaad869", "sha256_in_prefix": "bb2a75933611e926b0401b2a9726975df231271e19da633c3239999fcaaad869", "size_in_bytes": 505}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/_adapters.py", "path_type": "hardlink", "sha256": "be9ac919b51e1db6a35fa5c2b8c3fa27794caea0a2f8ffcc4e5ce225447b8df9", "sha256_in_prefix": "be9ac919b51e1db6a35fa5c2b8c3fa27794caea0a2f8ffcc4e5ce225447b8df9", "size_in_bytes": 4482}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/_common.py", "path_type": "hardlink", "sha256": "6e5b78f99b479db50fcd04323cfee32c6825ffce9bb485b966122c1217258680", "sha256_in_prefix": "6e5b78f99b479db50fcd04323cfee32c6825ffce9bb485b966122c1217258680", "size_in_bytes": 5571}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/_itertools.py", "path_type": "hardlink", "sha256": "7838ac57a46a88d64ea202d25dfe8b3861ce61cefd14680faca34bcc52e60ab5", "sha256_in_prefix": "7838ac57a46a88d64ea202d25dfe8b3861ce61cefd14680faca34bcc52e60ab5", "size_in_bytes": 1277}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/abc.py", "path_type": "hardlink", "sha256": "50a354f677040e4651077b717066f758bc6c2f68a3bbd25b68b4c8f9d7cb13fe", "sha256_in_prefix": "50a354f677040e4651077b717066f758bc6c2f68a3bbd25b68b4c8f9d7cb13fe", "size_in_bytes": 5162}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/compat/py38.py", "path_type": "hardlink", "sha256": "31686eb775ec009c0161469ae506f60280ab64da9c42355384ff8fd6e06813fe", "sha256_in_prefix": "31686eb775ec009c0161469ae506f60280ab64da9c42355384ff8fd6e06813fe", "size_in_bytes": 230}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/compat/py39.py", "path_type": "hardlink", "sha256": "59f967e2e4144a1373d577421beb681bafd8d16ae55263bd273a6fb5c7d0f82c", "sha256_in_prefix": "59f967e2e4144a1373d577421beb681bafd8d16ae55263bd273a6fb5c7d0f82c", "size_in_bytes": 184}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/functional.py", "path_type": "hardlink", "sha256": "98b5380f04a587cff62175aac0a39f3d5c7246a004a41dc1e174df471af75f73", "sha256_in_prefix": "98b5380f04a587cff62175aac0a39f3d5c7246a004a41dc1e174df471af75f73", "size_in_bytes": 2651}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/future/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/future/adapters.py", "path_type": "hardlink", "sha256": "d7e305d9545c081bad85c0b538c7d920b53da379306789d707696ead7a5a200c", "sha256_in_prefix": "d7e305d9545c081bad85c0b538c7d920b53da379306789d707696ead7a5a200c", "size_in_bytes": 2940}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/readers.py", "path_type": "hardlink", "sha256": "58d2aeac11c756ef4456d5215a43a3d9fc47e741cfeee6a7345baea40a87d92f", "sha256_in_prefix": "58d2aeac11c756ef4456d5215a43a3d9fc47e741cfeee6a7345baea40a87d92f", "size_in_bytes": 5863}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/simple.py", "path_type": "hardlink", "sha256": "090dd3888305889b3ff34a3eef124bd44a5b5145676b8f8d183ad24d0dc75b66", "sha256_in_prefix": "090dd3888305889b3ff34a3eef124bd44a5b5145676b8f8d183ad24d0dc75b66", "size_in_bytes": 2584}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/_path.py", "path_type": "hardlink", "sha256": "9e4bf77a4ec3d54f3df2ff76d6b61b95d0c2b4aae2da8c983a2dc426a1a31065", "sha256_in_prefix": "9e4bf77a4ec3d54f3df2ff76d6b61b95d0c2b4aae2da8c983a2dc426a1a31065", "size_in_bytes": 1289}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/compat/py312.py", "path_type": "hardlink", "sha256": "a9c5a3a59850a36a04b1dc089514501ebb0c18396d9054e7113786edf2dd512f", "sha256_in_prefix": "a9c5a3a59850a36a04b1dc089514501ebb0c18396d9054e7113786edf2dd512f", "size_in_bytes": 364}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/compat/py39.py", "path_type": "hardlink", "sha256": "9514e4d115803846fd473020bdd467a895060812dcdeaa05430cee2526bfccfe", "sha256_in_prefix": "9514e4d115803846fd473020bdd467a895060812dcdeaa05430cee2526bfccfe", "size_in_bytes": 329}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data01/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data01/binary.file", "path_type": "hardlink", "sha256": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "sha256_in_prefix": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "size_in_bytes": 4}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/binary.file", "path_type": "hardlink", "sha256": "c6d44cf418f610e3fe9e1d9294ff43def81c6cdcad6cbb1820cff48d3aa4355d", "sha256_in_prefix": "c6d44cf418f610e3fe9e1d9294ff43def81c6cdcad6cbb1820cff48d3aa4355d", "size_in_bytes": 4}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-16.file", "path_type": "hardlink", "sha256": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "sha256_in_prefix": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "size_in_bytes": 44}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data01/utf-8.file", "path_type": "hardlink", "sha256": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "sha256_in_prefix": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "size_in_bytes": 20}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data02/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/resource1.txt", "path_type": "hardlink", "sha256": "d747e529a73b73e5d7173277b7e001e4c263941cbffdd499bcf13f74e9b6aba5", "sha256_in_prefix": "d747e529a73b73e5d7173277b7e001e4c263941cbffdd499bcf13f74e9b6aba5", "size_in_bytes": 13}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data02/subdirectory/subsubdir/resource.txt", "path_type": "hardlink", "sha256": "8e7ac1073b7162bb50724edc9959dce314153b8f9a8330190c64859005ad945c", "sha256_in_prefix": "8e7ac1073b7162bb50724edc9959dce314153b8f9a8330190c64859005ad945c", "size_in_bytes": 10}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/resource2.txt", "path_type": "hardlink", "sha256": "96dda36cddd3327f5088528cf37d97dfd6d4ffad94a6d0dd524a18ce4bc46e5d", "sha256_in_prefix": "96dda36cddd3327f5088528cf37d97dfd6d4ffad94a6d0dd524a18ce4bc46e5d", "size_in_bytes": 13}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/binary.file", "path_type": "hardlink", "sha256": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "sha256_in_prefix": "054edec1d0211f624fed0cbca9d4f9400b0e491c43742af2c5b0abebf0c990d8", "size_in_bytes": 4}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/subdirectory/binary.file", "path_type": "hardlink", "sha256": "71b92110bf135c85581c8a128f6a19c0f6aca752b0c6c91e3571899cf09b145d", "sha256_in_prefix": "71b92110bf135c85581c8a128f6a19c0f6aca752b0c6c91e3571899cf09b145d", "size_in_bytes": 4}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-16.file", "path_type": "hardlink", "sha256": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "sha256_in_prefix": "b79abdaa1c57d2b62a22d04e33c0f7ca5c06f911eb9ce62d7932ed42beac17b8", "size_in_bytes": 44}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/namespacedata01/utf-8.file", "path_type": "hardlink", "sha256": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "sha256_in_prefix": "9305a0606e3243e645d97fd603ae848d83e6c49467fb0f1a48e892f5ef2d2986", "size_in_bytes": 20}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_compatibilty_files.py", "path_type": "hardlink", "sha256": "f7937f47b6a293c72f9c4eac049a6cc663f42b9a6539644824381b6a50fe1e9c", "sha256_in_prefix": "f7937f47b6a293c72f9c4eac049a6cc663f42b9a6539644824381b6a50fe1e9c", "size_in_bytes": 3314}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_contents.py", "path_type": "hardlink", "sha256": "ef41d6de62ff86fd39126bfe3a0766c282e15f18edb95c62595694a6045a4560", "sha256_in_prefix": "ef41d6de62ff86fd39126bfe3a0766c282e15f18edb95c62595694a6045a4560", "size_in_bytes": 930}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_custom.py", "path_type": "hardlink", "sha256": "42b1d9a885a5d1ef9fb1045f9b4c9c87cb2d3a52893ac008537acae9039cc8dd", "sha256_in_prefix": "42b1d9a885a5d1ef9fb1045f9b4c9c87cb2d3a52893ac008537acae9039cc8dd", "size_in_bytes": 1221}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_files.py", "path_type": "hardlink", "sha256": "39c4a162edf79027325e50f265254f909344d3c87e37fe1b40e2d5d90692a97d", "sha256_in_prefix": "39c4a162edf79027325e50f265254f909344d3c87e37fe1b40e2d5d90692a97d", "size_in_bytes": 3472}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_functional.py", "path_type": "hardlink", "sha256": "0720955620306f63c894abc334912a4d9d1a2d91a9165e6a6bb08c097fbb1ca3", "sha256_in_prefix": "0720955620306f63c894abc334912a4d9d1a2d91a9165e6a6bb08c097fbb1ca3", "size_in_bytes": 8591}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_open.py", "path_type": "hardlink", "sha256": "71c9b36ce7846bacd3778ca6659f32212ade71fb985748e1a27f9575dab2b2ee", "sha256_in_prefix": "71c9b36ce7846bacd3778ca6659f32212ade71fb985748e1a27f9575dab2b2ee", "size_in_bytes": 2778}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_path.py", "path_type": "hardlink", "sha256": "c7caf6809c46de114cf7108e14d920987617c6c32574c2d3496fc06587f597e0", "sha256_in_prefix": "c7caf6809c46de114cf7108e14d920987617c6c32574c2d3496fc06587f597e0", "size_in_bytes": 2009}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_read.py", "path_type": "hardlink", "sha256": "eedb082d0d8da2a546150c611eac41c1ce6159c37c6ffde27688c2b2ccd335f4", "sha256_in_prefix": "eedb082d0d8da2a546150c611eac41c1ce6159c37c6ffde27688c2b2ccd335f4", "size_in_bytes": 3112}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_reader.py", "path_type": "hardlink", "sha256": "21c2145da88f02db9a846578fd94f861714b30cb0999c33588ec6a7481f601ae", "sha256_in_prefix": "21c2145da88f02db9a846578fd94f861714b30cb0999c33588ec6a7481f601ae", "size_in_bytes": 5001}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/test_resource.py", "path_type": "hardlink", "sha256": "7dc17c5a067aac30934459f1b4051b76268d7b81b6df21a8bd3d676f675cee5b", "sha256_in_prefix": "7dc17c5a067aac30934459f1b4051b76268d7b81b6df21a8bd3d676f675cee5b", "size_in_bytes": 7823}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/util.py", "path_type": "hardlink", "sha256": "be35731325f45f64644cdfb01a2422a656b2a7db19a26e090e32629d335ec28b", "sha256_in_prefix": "be35731325f45f64644cdfb01a2422a656b2a7db19a26e090e32629d335ec28b", "size_in_bytes": 4745}, {"_path": "site-packages/setuptools/_vendor/importlib_resources/tests/zip.py", "path_type": "hardlink", "sha256": "d8c2a617cfa8b170494a7a9c513b8051e93ffad481de22a6213f6a3e172c3ac3", "sha256_in_prefix": "d8c2a617cfa8b170494a7a9c513b8051e93ffad481de22a6213f6a3e172c3ac3", "size_in_bytes": 783}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "5f7a283b75a709fccd481aea42379f083d4f3801753365922e6b0732042515d9", "sha256_in_prefix": "5f7a283b75a709fccd481aea42379f083d4f3801753365922e6b0732042515d9", "size_in_bytes": 3204}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "38a6898306293627c81e2b2d8a93e5f6857d5f7edb73f0334e8d9a53dad53b6e", "sha256_in_prefix": "38a6898306293627c81e2b2d8a93e5f6857d5f7edb73f0334e8d9a53dad53b6e", "size_in_bytes": 2565}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/packaging-24.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "76dc366cd996090f569cca0addb93f7a52f5b2f4a58a45ed2e9661085201f521", "sha256_in_prefix": "76dc366cd996090f569cca0addb93f7a52f5b2f4a58a45ed2e9661085201f521", "size_in_bytes": 496}, {"_path": "site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "fcb7095b860d2b2c18b25e35ebd076ba4291ab0c63c6cb7ff07d0545540a973f", "sha256_in_prefix": "fcb7095b860d2b2c18b25e35ebd076ba4291ab0c63c6cb7ff07d0545540a973f", "size_in_bytes": 3282}, {"_path": "site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "5e8e15d0f673f2c6ee5426d39e2d2dd424740077a2affee26f8953995f2c703e", "sha256_in_prefix": "5e8e15d0f673f2c6ee5426d39e2d2dd424740077a2affee26f8953995f2c703e", "size_in_bytes": 9586}, {"_path": "site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "756292aa7e52a7e8c398e1be5b719f2c72a3c217f522cce76d3ef55650680793", "sha256_in_prefix": "756292aa7e52a7e8c398e1be5b719f2c72a3c217f522cce76d3ef55650680793", "size_in_bytes": 10671}, {"_path": "site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "28836e4a4275daef92ca828d4f2fe91cd1807cc52dc4dbd9e77a80d7300a70a2", "sha256_in_prefix": "28836e4a4275daef92ca828d4f2fe91cd1807cc52dc4dbd9e77a80d7300a70a2", "size_in_bytes": 32349}, {"_path": "site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "ae3a5cde1a09bae9d1213e83747ee02d39d0e63e50292b968e84c2e6c747b472", "sha256_in_prefix": "ae3a5cde1a09bae9d1213e83747ee02d39d0e63e50292b968e84c2e6c747b472", "size_in_bytes": 39714}, {"_path": "site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "cbc11b85e3aef564bbb3e31e6da5cc707305fa3cec03f0b52f3e57453892cb8c", "sha256_in_prefix": "cbc11b85e3aef564bbb3e31e6da5cc707305fa3cec03f0b52f3e57453892cb8c", "size_in_bytes": 18883}, {"_path": "site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "3407585309e500ea646adfd1b616af5fc6b4ed8b95c6018bfefc2bc7bdc64833", "sha256_in_prefix": "3407585309e500ea646adfd1b616af5fc6b4ed8b95c6018bfefc2bc7bdc64833", "size_in_bytes": 5287}, {"_path": "site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "5741f748e8fff307abaefa2badbe900cb46195ca9212b3534e40afbdfb338432", "sha256_in_prefix": "5741f748e8fff307abaefa2badbe900cb46195ca9212b3534e40afbdfb338432", "size_in_bytes": 16198}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "site-packages/setuptools/_vendor/ruff.toml", "path_type": "hardlink", "sha256": "5d695f64b5cbc4ce0efe8b0a4199b8cce2650a69caa65d96d3965bc73d09ba77", "sha256_in_prefix": "5d695f64b5cbc4ce0efe8b0a4199b8cce2650a69caa65d96d3965bc73d09ba77", "size_in_bytes": 16}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "59bac22b00a59d3e5608a56b8cf8efc43831a36b72792ee4389c9cd4669c7841", "sha256_in_prefix": "59bac22b00a59d3e5608a56b8cf8efc43831a36b72792ee4389c9cd4669c7841", "size_in_bytes": 2153}, {"_path": "site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "783e654742611af88cd9f00bf01a431a219db536556e63ff981c7bd673070ac9", "sha256_in_prefix": "783e654742611af88cd9f00bf01a431a219db536556e63ff981c7bd673070ac9", "size_in_bytes": 4557}, {"_path": "site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "0fa8e11f4d1e3336e0ad718078ec157c3e62fa508030cc9cb86d4bd2eb1e0e5a", "sha256_in_prefix": "0fa8e11f4d1e3336e0ad718078ec157c3e62fa508030cc9cb86d4bd2eb1e0e5a", "size_in_bytes": 59}, {"_path": "site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "3680a78c9e03144678e44a3ed817572ec5890b01a46a2b75b69ff5ee96a5795c", "sha256_in_prefix": "3680a78c9e03144678e44a3ed817572ec5890b01a46a2b75b69ff5ee96a5795c", "size_in_bytes": 746}, {"_path": "site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "38a272a7d13cdf5cc9af1a117e633d0203a30721b5081fa9cc5e645d016668a9", "sha256_in_prefix": "38a272a7d13cdf5cc9af1a117e633d0203a30721b5081fa9cc5e645d016668a9", "size_in_bytes": 20938}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "7813619cfc164ed74a0091f2efb96fcfb80e43912edc66af1ae817c614ac9fe5", "sha256_in_prefix": "7813619cfc164ed74a0091f2efb96fcfb80e43912edc66af1ae817c614ac9fe5", "size_in_bytes": 4264}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "a897296062aa75fc353fa05e9603751e7fecb8d80ce9bbf211616565eb925b1d", "sha256_in_prefix": "a897296062aa75fc353fa05e9603751e7fecb8d80ce9bbf211616565eb925b1d", "size_in_bytes": 9439}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "1e75ba38f74df7dde9b12b6fc25e3dc6dc76930ee1a156deea7bf099ff16b0a2", "sha256_in_prefix": "1e75ba38f74df7dde9b12b6fc25e3dc6dc76930ee1a156deea7bf099ff16b0a2", "size_in_bytes": 16103}, {"_path": "site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "abec420aa4802bb1f3c99c4af40ebf1c05a686a4b5a01e01170d7eac74310624", "sha256_in_prefix": "abec420aa4802bb1f3c99c4af40ebf1c05a686a4b5a01e01170d7eac74310624", "size_in_bytes": 5884}, {"_path": "site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "sha256_in_prefix": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "size_in_bytes": 621}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "0ed2435a864cbe7061e2578d3033c63a9ad053d77f769eaaf8c995d14fbee317", "sha256_in_prefix": "0ed2435a864cbe7061e2578d3033c63a9ad053d77f769eaaf8c995d14fbee317", "size_in_bytes": 7694}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "f02ed7d4a657a61a8b915a5976099ef1b9b653d08f437b669f1a563296c7d263", "sha256_in_prefix": "f02ed7d4a657a61a8b915a5976099ef1b9b653d08f437b669f1a563296c7d263", "size_in_bytes": 7332}, {"_path": "site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "7ea271da0942fe13ceab731dd8c5e69323e56ed0116423f195b8a00e5e5f7a18", "sha256_in_prefix": "7ea271da0942fe13ceab731dd8c5e69323e56ed0116423f195b8a00e5e5f7a18", "size_in_bytes": 19151}, {"_path": "site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "25b0393e6a676760074111554f6af3e6efcf8c916ce7ae3b18e20ee4eb89cc34", "sha256_in_prefix": "25b0393e6a676760074111554f6af3e6efcf8c916ce7ae3b18e20ee4eb89cc34", "size_in_bytes": 397}, {"_path": "site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "b36308bf9bd673b37fe661f389420fbe5e958e83c8509b22bf4712e225c4cb93", "sha256_in_prefix": "b36308bf9bd673b37fe661f389420fbe5e958e83c8509b22bf4712e225c4cb93", "size_in_bytes": 4227}, {"_path": "site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "6a218cc3501e378aea263af736bdd25436cb5e3823b9fae2c1c30cd670668876", "sha256_in_prefix": "6a218cc3501e378aea263af736bdd25436cb5e3823b9fae2c1c30cd670668876", "size_in_bytes": 2383}, {"_path": "site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "326cd3e0607d7bb15427e2b2a16cb20c645d93beeacec83c375550bd52f1df91", "sha256_in_prefix": "326cd3e0607d7bb15427e2b2a16cb20c645d93beeacec83c375550bd52f1df91", "size_in_bytes": 16526}, {"_path": "site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "f7dc151416bc105de01db12ad744288ee61ca71df35ff34b09864da5b1bae865", "sha256_in_prefix": "f7dc151416bc105de01db12ad744288ee61ca71df35ff34b09864da5b1bae865", "size_in_bytes": 1427}, {"_path": "site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "ea6b9f861f3a733bcd9f76c62a0e6036fc1cdb8eb3b2571cd19363336b9c9738", "sha256_in_prefix": "ea6b9f861f3a733bcd9f76c62a0e6036fc1cdb8eb3b2571cd19363336b9c9738", "size_in_bytes": 22883}, {"_path": "site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "0bd4d0afbb7fab74de856b1cc443d46d71fbaf4f290b2374b43abdaf13c5a28e", "sha256_in_prefix": "0bd4d0afbb7fab74de856b1cc443d46d71fbaf4f290b2374b43abdaf13c5a28e", "size_in_bytes": 6028}, {"_path": "site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "bf1a795a6cd1e914189a15d7c80cf8c34d30f0def1c02a8f99ab8dba4833ac9d", "sha256_in_prefix": "bf1a795a6cd1e914189a15d7c80cf8c34d30f0def1c02a8f99ab8dba4833ac9d", "size_in_bytes": 4736}, {"_path": "site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "0ee1f835c81a430576b22f7edbbb815a669dfb401dc42e54edc0def132a99c80", "sha256_in_prefix": "0ee1f835c81a430576b22f7edbbb815a669dfb401dc42e54edc0def132a99c80", "size_in_bytes": 18264}, {"_path": "site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "1b61f1bbe5aace1e9dc566d18d11df112fd29656141942abc87ff4102b31d955", "sha256_in_prefix": "1b61f1bbe5aace1e9dc566d18d11df112fd29656141942abc87ff4102b31d955", "size_in_bytes": 15327}, {"_path": "site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "49243120a6573fbfa450e7d34f86b7588a33b2c8833a2bc741feb5a34df850ee", "sha256_in_prefix": "49243120a6573fbfa450e7d34f86b7588a33b2c8833a2bc741feb5a34df850ee", "size_in_bytes": 6854}, {"_path": "site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "16e785a88fadefe29e3f513c3f21bd0347f8b4309faf0d175dfeef9da6078d8e", "sha256_in_prefix": "16e785a88fadefe29e3f513c3f21bd0347f8b4309faf0d175dfeef9da6078d8e", "size_in_bytes": 3508}, {"_path": "site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "258a6df184d3f82ab3362750e09c9589f0bc33fcdf275892b5d45c1b2ae61db7", "sha256_in_prefix": "258a6df184d3f82ab3362750e09c9589f0bc33fcdf275892b5d45c1b2ae61db7", "size_in_bytes": 88305}, {"_path": "site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "a84d33564e2acc8302f50d7cfd48bc8a824b6b9e146dd9852aa4b1bc6c91d262", "sha256_in_prefix": "a84d33564e2acc8302f50d7cfd48bc8a824b6b9e146dd9852aa4b1bc6c91d262", "size_in_bytes": 35665}, {"_path": "site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "fef4a920d30ff30e6878cafd907ed5a920e9e97f16157868c8c8f8054b39138d", "sha256_in_prefix": "fef4a920d30ff30e6878cafd907ed5a920e9e97f16157868c8c8f8054b39138d", "size_in_bytes": 25610}, {"_path": "site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "a8e4fe2594814b9b5eded1a3bd7af702a8288219fa1c8f70d547784a432b54dd", "sha256_in_prefix": "a8e4fe2594814b9b5eded1a3bd7af702a8288219fa1c8f70d547784a432b54dd", "size_in_bytes": 6208}, {"_path": "site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "6eb6114466f4ffccc9dfe7d0d1f9e47970b35f6ada2cd5f4e496bb9fbddc2492", "sha256_in_prefix": "6eb6114466f4ffccc9dfe7d0d1f9e47970b35f6ada2cd5f4e496bb9fbddc2492", "size_in_bytes": 2046}, {"_path": "site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "dfefc9467be74314c3d25495f40b67c68930b48a6f35a3c194a1a9da2e52e71f", "sha256_in_prefix": "dfefc9467be74314c3d25495f40b67c68930b48a6f35a3c194a1a9da2e52e71f", "size_in_bytes": 4133}, {"_path": "site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "9906c8c7d72e30849a123d853a2e98bbbfeefeade0a3f6de07a829870042af02", "sha256_in_prefix": "9906c8c7d72e30849a123d853a2e98bbbfeefeade0a3f6de07a829870042af02", "size_in_bytes": 2614}, {"_path": "site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "f6bf20ded91bbe3ef239592be9cd9c99cc6fe4b36ac808a30036e68c9fd9cb6e", "sha256_in_prefix": "f6bf20ded91bbe3ef239592be9cd9c99cc6fe4b36ac808a30036e68c9fd9cb6e", "size_in_bytes": 2145}, {"_path": "site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "99500f31120613df2097a7974370b65a8faa3ce825f656c7f90fd8b1b2eea9e8", "sha256_in_prefix": "99500f31120613df2097a7974370b65a8faa3ce825f656c7f90fd8b1b2eea9e8", "size_in_bytes": 657}, {"_path": "site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "9066c66ef3271e6dc65fc0cc40088a3f27efcd6aadba55849d93796d3db65464", "sha256_in_prefix": "9066c66ef3271e6dc65fc0cc40088a3f27efcd6aadba55849d93796d3db65464", "size_in_bytes": 7277}, {"_path": "site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "323272fb4a428e7282a2e62583fa2e4f20761d99fd1b3e0263426ea5d5c4de7a", "sha256_in_prefix": "323272fb4a428e7282a2e62583fa2e4f20761d99fd1b3e0263426ea5d5c4de7a", "size_in_bytes": 5019}, {"_path": "site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "b1d9b9ed36dd286d439fdbd79b515fb552d1cebf8fe7ea9f06038fcf60a34502", "sha256_in_prefix": "b1d9b9ed36dd286d439fdbd79b515fb552d1cebf8fe7ea9f06038fcf60a34502", "size_in_bytes": 15457}, {"_path": "site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "09c9bcea95ca086f8bc5bed174e40bc835b297d40fb5f86bbbb570fe0a5581a7", "sha256_in_prefix": "09c9bcea95ca086f8bc5bed174e40bc835b297d40fb5f86bbbb570fe0a5581a7", "size_in_bytes": 18737}, {"_path": "site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "91dd12598aeca7721717d28600cf10a5e68aa46c8cb0d80bfad8e1f533df8726", "sha256_in_prefix": "91dd12598aeca7721717d28600cf10a5e68aa46c8cb0d80bfad8e1f533df8726", "size_in_bytes": 1625}, {"_path": "site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "4fc46f2528b9dd805f0c1e2644fb85ea0b0fc71f3ebbc2985f9eb2deaa24a7fe", "sha256_in_prefix": "4fc46f2528b9dd805f0c1e2644fb85ea0b0fc71f3ebbc2985f9eb2deaa24a7fe", "size_in_bytes": 335460}, {"_path": "site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "323cfa463c7504c0f0d974cc01f4beb0ce71e45bf9697d9993fab933feeb7ff7", "sha256_in_prefix": "323cfa463c7504c0f0d974cc01f4beb0ce71e45bf9697d9993fab933feeb7ff7", "size_in_bytes": 12814}, {"_path": "site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "8ec65451a53ff2ecbe6739490b49219a91a30954656d5fac17230893e4866084", "sha256_in_prefix": "8ec65451a53ff2ecbe6739490b49219a91a30954656d5fac17230893e4866084", "size_in_bytes": 15659}, {"_path": "site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "a71abeb1888c40f33dc00ba9700156131078ef723e67553e66906c99ed4b0333", "sha256_in_prefix": "a71abeb1888c40f33dc00ba9700156131078ef723e67553e66906c99ed4b0333", "size_in_bytes": 18142}, {"_path": "site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "ec33002f4fafa387d3ebf7691b55f6d0abe2e12f9c0e37c77b993f8330274149", "sha256_in_prefix": "ec33002f4fafa387d3ebf7691b55f6d0abe2e12f9c0e37c77b993f8330274149", "size_in_bytes": 25634}, {"_path": "site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "22c3dd2c113a5e9a6d532a7c5ad8a1383f0ef8f067af645ecde88a0454f94ad7", "sha256_in_prefix": "22c3dd2c113a5e9a6d532a7c5ad8a1383f0ef8f067af645ecde88a0454f94ad7", "size_in_bytes": 5542}, {"_path": "site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "90405cf2faa2d59abc908187f34ab443fc69e1f15d1da6d040cd841a0d9e198d", "sha256_in_prefix": "90405cf2faa2d59abc908187f34ab443fc69e1f15d1da6d040cd841a0d9e198d", "size_in_bytes": 21104}, {"_path": "site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "e9bceb5738643dd9c1d40291123307dfb75e2b22a97ab6150f5e724f51f9e4d0", "sha256_in_prefix": "e9bceb5738643dd9c1d40291123307dfb75e2b22a97ab6150f5e724f51f9e4d0", "size_in_bytes": 36729}, {"_path": "site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "a9d1c3e45a2784d33a82477ceb7fb8f5869c497911fe4ce1dc3d256348765592", "sha256_in_prefix": "a9d1c3e45a2784d33a82477ceb7fb8f5869c497911fe4ce1dc3d256348765592", "size_in_bytes": 2988}, {"_path": "site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "779a05f34db64d6128d19ed6b13f857efaf2e70f5bbdfcf5866dcff3ddbf21d1", "sha256_in_prefix": "779a05f34db64d6128d19ed6b13f857efaf2e70f5bbdfcf5866dcff3ddbf21d1", "size_in_bytes": 6457}, {"_path": "site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "88c0b2d25862ac5dbdb33d9f6bd07a295195bd49e2cb5d5216974b9755026fe2", "sha256_in_prefix": "88c0b2d25862ac5dbdb33d9f6bd07a295195bd49e2cb5d5216974b9755026fe2", "size_in_bytes": 4852}, {"_path": "site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "585f056624173f6acdb3f2e95fff02fc0f25eec662be7e57ce4f60ea5406da0e", "sha256_in_prefix": "585f056624173f6acdb3f2e95fff02fc0f25eec662be7e57ce4f60ea5406da0e", "size_in_bytes": 4970}, {"_path": "site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "56c601bd986292ffa8fee25ceb4c8cfb6fcf0900793f9bd21672e5e42e412790", "sha256_in_prefix": "56c601bd986292ffa8fee25ceb4c8cfb6fcf0900793f9bd21672e5e42e412790", "size_in_bytes": 812}, {"_path": "site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "05829dd1bcdb882bd407d5c4b5f09869887d17f40da7162346c1765e6c39d64f", "sha256_in_prefix": "05829dd1bcdb882bd407d5c4b5f09869887d17f40da7162346c1765e6c39d64f", "size_in_bytes": 1241}, {"_path": "site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "186cdcef5c8491fc0b575f87a97470c2c9f38676fdee4d43dd1ec7f0413fd33e", "sha256_in_prefix": "186cdcef5c8491fc0b575f87a97470c2c9f38676fdee4d43dd1ec7f0413fd33e", "size_in_bytes": 190}, {"_path": "site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "7f1effd349e3901d9c0ab76d4d6a990cacdd2ac4f3f5df461d9b4c068ce0c0ea", "sha256_in_prefix": "7f1effd349e3901d9c0ab76d4d6a990cacdd2ac4f3f5df461d9b4c068ce0c0ea", "size_in_bytes": 3573}, {"_path": "site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "1e5fb47f772ec58f74b3777b1f1585ea5813a751bdf4eeb9c1e4f0ec95900c49", "sha256_in_prefix": "1e5fb47f772ec58f74b3777b1f1585ea5813a751bdf4eeb9c1e4f0ec95900c49", "size_in_bytes": 40791}, {"_path": "site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "f0daf135ae19ae47ef8682154ced8fcea2f284ee80d429542c35fa9aec38b3ad", "sha256_in_prefix": "f0daf135ae19ae47ef8682154ced8fcea2f284ee80d429542c35fa9aec38b3ad", "size_in_bytes": 3155}, {"_path": "site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "f71aafc30060b1ef4baba47a211717a9258948f233fdc1d9cf5a5c90d4e04273", "sha256_in_prefix": "f71aafc30060b1ef4baba47a211717a9258948f233fdc1d9cf5a5c90d4e04273", "size_in_bytes": 39051}, {"_path": "site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "88f9b320fbc70e5e3673014ad209d4a56908ce28175e3753232ffc1181a0c904", "sha256_in_prefix": "88f9b320fbc70e5e3673014ad209d4a56908ce28175e3753232ffc1181a0c904", "size_in_bytes": 14550}, {"_path": "site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "d16ac85d90df1671644558a8ddaf99e917df44c65b73702e26cb56693ae8af18", "sha256_in_prefix": "d16ac85d90df1671644558a8ddaf99e917df44c65b73702e26cb56693ae8af18", "size_in_bytes": 1762}, {"_path": "site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "715f73023ad9fffb01aedb13ec60a740b74aa6cbae6e589907e3a107a302c4c9", "sha256_in_prefix": "715f73023ad9fffb01aedb13ec60a740b74aa6cbae6e589907e3a107a302c4c9", "size_in_bytes": 19258}, {"_path": "site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "c899a1ab9d8ca933c47ede9039295947cc8de9d90956d94e713a3d0fba6a1975", "sha256_in_prefix": "c899a1ab9d8ca933c47ede9039295947cc8de9d90956d94e713a3d0fba6a1975", "size_in_bytes": 8111}, {"_path": "site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "33141ec0c72765c65364086825b191315bd1b3679195b1d0b2700904eaa51de7", "sha256_in_prefix": "33141ec0c72765c65364086825b191315bd1b3679195b1d0b2700904eaa51de7", "size_in_bytes": 12398}, {"_path": "site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "8777fd7cfcbea31b42fc73e0c6d3063352e0745cf4d5162d4cbe3bb9b159a793", "sha256_in_prefix": "8777fd7cfcbea31b42fc73e0c6d3063352e0745cf4d5162d4cbe3bb9b159a793", "size_in_bytes": 3072}, {"_path": "site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "b75dacbae8cb5b813ec404437c4c72d354664a4185d65da5b01bd7aa104f7edb", "sha256_in_prefix": "b75dacbae8cb5b813ec404437c4c72d354664a4185d65da5b01bd7aa104f7edb", "size_in_bytes": 33361}, {"_path": "site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "79bcf6253be29db1345ca9be2371da4a4ba79baa0494d688cdcad37ef7fa3984", "sha256_in_prefix": "79bcf6253be29db1345ca9be2371da4a4ba79baa0494d688cdcad37ef7fa3984", "size_in_bytes": 8288}, {"_path": "site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "65124e28a4065286b4de28cf16b162a8d21de36f046fdbb2ff2b0e66d6a050db", "sha256_in_prefix": "65124e28a4065286b4de28cf16b162a8d21de36f046fdbb2ff2b0e66d6a050db", "size_in_bytes": 2403}, {"_path": "site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "71d7d9b16a459834526d9795bbeebd4f442209cfb6e3aadb523b64b81ee20626", "sha256_in_prefix": "71d7d9b16a459834526d9795bbeebd4f442209cfb6e3aadb523b64b81ee20626", "size_in_bytes": 1851}, {"_path": "site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "0fe4cd8b73498677967f4c890a6a59bf6ade3f87e7b9f6c9c14e34317b0c6909", "sha256_in_prefix": "0fe4cd8b73498677967f4c890a6a59bf6ade3f87e7b9f6c9c14e34317b0c6909", "size_in_bytes": 19480}, {"_path": "site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "5e940f141ba1bd71fbd4fe9868b983c7f8d7cdf0f0ea3d7b10a52b72af2f9196", "sha256_in_prefix": "5e940f141ba1bd71fbd4fe9868b983c7f8d7cdf0f0ea3d7b10a52b72af2f9196", "size_in_bytes": 10036}, {"_path": "site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "8a92dbf9ec21a11daa9deb4523cbf60d9320449e8cea6a9d839d8e871e9556ed", "sha256_in_prefix": "8a92dbf9ec21a11daa9deb4523cbf60d9320449e8cea6a9d839d8e871e9556ed", "size_in_bytes": 33570}, {"_path": "site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "d8c3a23df67d0afc0839de53355ea10aaf54ad70371d7266e6f8f918aee8a6cc", "sha256_in_prefix": "d8c3a23df67d0afc0839de53355ea10aaf54ad70371d7266e6f8f918aee8a6cc", "size_in_bytes": 14185}, {"_path": "site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "e76fc711d03dedd81a32fb9db272190f2ec86193ef1dbe64e987fd000d7de780", "sha256_in_prefix": "e76fc711d03dedd81a32fb9db272190f2ec86193ef1dbe64e987fd000d7de780", "size_in_bytes": 22562}, {"_path": "site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "ef15a695282323faffb54b1c055b7e920d893f5d04b7984c348d1ff2732d28f0", "sha256_in_prefix": "ef15a695282323faffb54b1c055b7e920d893f5d04b7984c348d1ff2732d28f0", "size_in_bytes": 12183}, {"_path": "site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "9c6ef2e3522e959f698c7f9b8b039cb5c40e69fa516cd633d08e6c0d7f1dd690", "sha256_in_prefix": "9c6ef2e3522e959f698c7f9b8b039cb5c40e69fa516cd633d08e6c0d7f1dd690", "size_in_bytes": 8811}, {"_path": "site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "7b1aaa5395d292c4625e352ee26c754a637efe39cfa083cf5b0b0ba8173c7d04", "sha256_in_prefix": "7b1aaa5395d292c4625e352ee26c754a637efe39cfa083cf5b0b0ba8173c7d04", "size_in_bytes": 7090}, {"_path": "site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "669749db00b2ab6cc6c6c0407234f65fbd95b23d185c8a56fbb7e1ca76f7eb52", "sha256_in_prefix": "669749db00b2ab6cc6c6c0407234f65fbd95b23d185c8a56fbb7e1ca76f7eb52", "size_in_bytes": 4747}, {"_path": "site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "d2851e846b290e21973b28950ca8392cf62c366b5552167c14e841a0d6278a0b", "sha256_in_prefix": "d2851e846b290e21973b28950ca8392cf62c366b5552167c14e841a0d6278a0b", "size_in_bytes": 53241}, {"_path": "site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "5b693c62670ef6c09995e22312d2f56ff7bd335e8a3b92fd6b51a04e6371dae6", "sha256_in_prefix": "5b693c62670ef6c09995e22312d2f56ff7bd335e8a3b92fd6b51a04e6371dae6", "size_in_bytes": 43304}, {"_path": "site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "32f256bc89f9619462def406a61e125b40061c27fbdc6dcbdc7773f409c06709", "sha256_in_prefix": "32f256bc89f9619462def406a61e125b40061c27fbdc6dcbdc7773f409c06709", "size_in_bytes": 44145}, {"_path": "site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "a57314aa27487c61b64ccfb967d3364fb0f3fce2cf0fa5fe697f10894a45bf65", "sha256_in_prefix": "a57314aa27487c61b64ccfb967d3364fb0f3fce2cf0fa5fe697f10894a45bf65", "size_in_bytes": 881}, {"_path": "site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "6c915788e62631dfbb66007d2ae7a1fef9a2881b530f78c314e0929334b2b3d4", "sha256_in_prefix": "6c915788e62631dfbb66007d2ae7a1fef9a2881b530f78c314e0929334b2b3d4", "size_in_bytes": 3441}, {"_path": "site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "f4c7aa27f8e0413944f4ce1637b62e142f626212ab4fd7ec24868a1b52fbde9e", "sha256_in_prefix": "f4c7aa27f8e0413944f4ce1637b62e142f626212ab4fd7ec24868a1b52fbde9e", "size_in_bytes": 2095}, {"_path": "site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "378518f18d53be33a229eacb1d217a2e41ee7a75c697cb70ea8fa2be58f77b90", "sha256_in_prefix": "378518f18d53be33a229eacb1d217a2e41ee7a75c697cb70ea8fa2be58f77b90", "size_in_bytes": 18761}, {"_path": "site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "cdec075d7e3c5e181898ec053ac870d1e353e344a1b14cede4c9d807d3f34473", "sha256_in_prefix": "cdec075d7e3c5e181898ec053ac870d1e353e344a1b14cede4c9d807d3f34473", "size_in_bytes": 8975}, {"_path": "site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b0b68a05192f43736923464d7b02235a2c024e6bae89ef083e4250cb1fb9917d", "sha256_in_prefix": "b0b68a05192f43736923464d7b02235a2c024e6bae89ef083e4250cb1fb9917d", "size_in_bytes": 4333}, {"_path": "site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "25d003b93d8c5a205221c34b777a3da02e32d9718fe456484fd0e9f3d45d5d3f", "sha256_in_prefix": "25d003b93d8c5a205221c34b777a3da02e32d9718fe456484fd0e9f3d45d5d3f", "size_in_bytes": 32440}, {"_path": "site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "3a9aeb76e514813c6e4c2c00b1223d4d49892458e3b6bfb76fab84b96e42358f", "sha256_in_prefix": "3a9aeb76e514813c6e4c2c00b1223d4d49892458e3b6bfb76fab84b96e42358f", "size_in_bytes": 8978}, {"_path": "site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "a55e673d45f22751081b3ad651be5c303c2b28a2b60c87fc7bed38a46951ed86", "sha256_in_prefix": "a55e673d45f22751081b3ad651be5c303c2b28a2b60c87fc7bed38a46951ed86", "size_in_bytes": 19259}, {"_path": "site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "d859a011f1465f9395d2454ea4f3c46480802fbe93adc35b7dadae4c2cecd77e", "sha256_in_prefix": "d859a011f1465f9395d2454ea4f3c46480802fbe93adc35b7dadae4c2cecd77e", "size_in_bytes": 7894}, {"_path": "site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "683bc86593ed327dde0b57294b8b5ad16ba065267bb29a0acb11aa65dec01f3c", "sha256_in_prefix": "683bc86593ed327dde0b57294b8b5ad16ba065267bb29a0acb11aa65dec01f3c", "size_in_bytes": 3181}, {"_path": "site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "5f3dde112ad811d3f47589557ab3fc040d1eb3e51b90544013880e9d7526c6e2", "sha256_in_prefix": "5f3dde112ad811d3f47589557ab3fc040d1eb3e51b90544013880e9d7526c6e2", "size_in_bytes": 3714}, {"_path": "site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c3e17c9fda9d76eb0309a742f0f589964679baf95791a1b2b5082791e9d91a2c", "sha256_in_prefix": "c3e17c9fda9d76eb0309a742f0f589964679baf95791a1b2b5082791e9d91a2c", "size_in_bytes": 8628}, {"_path": "site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}, {"_path": "lib/python3.12/site-packages/_distutils_hack/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/_distutils_hack/__pycache__/override.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_core_metadata.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_log.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/compat/__pycache__/py38.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/core.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/debug.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/dist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/errors.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/extension.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/log.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/compat/__pycache__/py38.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_ccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_cygwinccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_mingwccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_msvccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_unixccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/version.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_entry_points.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_imp.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_importlib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_itertools.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_normalization.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_path.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/_reqs.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_adapters.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_common.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/_itertools.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/abc.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py38.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/compat/__pycache__/py39.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/functional.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/future/__pycache__/adapters.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/readers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/__pycache__/simple.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/_path.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py312.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/compat/__pycache__/py39.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data01/subdirectory/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/one/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/data02/two/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_compatibilty_files.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_contents.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_custom.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_files.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_functional.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_open.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_path.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_read.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_reader.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/test_resource.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/importlib_resources/tests/__pycache__/zip.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/archive_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/build_meta.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/alias.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/build.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/build_clib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/build_ext.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/build_py.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/develop.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/dist_info.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/easy_install.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/egg_info.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/install.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/install_lib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/install_scripts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/rotate.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/saveopts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/sdist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/setopt.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/command/__pycache__/test.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/compat/__pycache__/py310.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/compat/__pycache__/py311.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/compat/__pycache__/py312.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/compat/__pycache__/py39.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/__pycache__/expand.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/config/__pycache__/setupcfg.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/depends.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/discovery.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/dist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/errors.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/extension.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/glob.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/installer.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/launch.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/logging.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/modified.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/monkey.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/msvc.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/namespaces.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/package_index.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/sandbox.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/contexts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/environment.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/fixtures.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/namespaces.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/server.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_depends.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_develop.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_dist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_extern.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_glob.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_logging.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/text.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/tests/__pycache__/textwrap.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/unicode_utils.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/version.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/warnings.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/setuptools/__pycache__/windows_support.cpython-312.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "6725235722095c547edd24275053c615158d6163f396550840aebd6e209e4738", "size": 777462, "subdir": "noarch", "timestamp": 1727249510000, "url": "https://conda.anaconda.org/conda-forge/noarch/setuptools-75.1.0-pyhd8ed1ab_0.conda", "version": "75.1.0"}