{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.8"], "extracted_package_dir": "/opt/anaconda3/pkgs/wheel-0.44.0-pyhd8ed1ab_0", "files": ["lib/python3.12/site-packages/wheel-0.44.0.dist-info/LICENSE.txt", "lib/python3.12/site-packages/wheel-0.44.0.dist-info/METADATA", "lib/python3.12/site-packages/wheel-0.44.0.dist-info/RECORD", "lib/python3.12/site-packages/wheel-0.44.0.dist-info/WHEEL", "lib/python3.12/site-packages/wheel-0.44.0.dist-info/entry_points.txt", "lib/python3.12/site-packages/wheel/__init__.py", "lib/python3.12/site-packages/wheel/__main__.py", "lib/python3.12/site-packages/wheel/_bdist_wheel.py", "lib/python3.12/site-packages/wheel/_setuptools_logging.py", "lib/python3.12/site-packages/wheel/bdist_wheel.py", "lib/python3.12/site-packages/wheel/cli/__init__.py", "lib/python3.12/site-packages/wheel/cli/convert.py", "lib/python3.12/site-packages/wheel/cli/pack.py", "lib/python3.12/site-packages/wheel/cli/tags.py", "lib/python3.12/site-packages/wheel/cli/unpack.py", "lib/python3.12/site-packages/wheel/macosx_libfile.py", "lib/python3.12/site-packages/wheel/metadata.py", "lib/python3.12/site-packages/wheel/util.py", "lib/python3.12/site-packages/wheel/vendored/__init__.py", "lib/python3.12/site-packages/wheel/vendored/packaging/__init__.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_elffile.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_manylinux.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_musllinux.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_parser.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_structures.py", "lib/python3.12/site-packages/wheel/vendored/packaging/_tokenizer.py", "lib/python3.12/site-packages/wheel/vendored/packaging/markers.py", "lib/python3.12/site-packages/wheel/vendored/packaging/requirements.py", "lib/python3.12/site-packages/wheel/vendored/packaging/specifiers.py", "lib/python3.12/site-packages/wheel/vendored/packaging/tags.py", "lib/python3.12/site-packages/wheel/vendored/packaging/utils.py", "lib/python3.12/site-packages/wheel/vendored/packaging/version.py", "lib/python3.12/site-packages/wheel/vendored/vendor.txt", "lib/python3.12/site-packages/wheel/wheelfile.py", "lib/python3.12/site-packages/wheel/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/__main__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/_bdist_wheel.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/convert.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/pack.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/tags.cpython-312.pyc", "lib/python3.12/site-packages/wheel/cli/__pycache__/unpack.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/metadata.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/util.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "lib/python3.12/site-packages/wheel/__pycache__/wheelfile.cpython-312.pyc", "bin/wheel"], "fn": "wheel-0.44.0-pyhd8ed1ab_0.conda", "license": "MIT", "link": {"source": "/opt/anaconda3/pkgs/wheel-0.44.0-pyhd8ed1ab_0", "type": 1}, "md5": "d44e3b085abcaef02983c6305b84b584", "name": "wheel", "noarch": "python", "package_tarball_full_path": "/opt/anaconda3/pkgs/wheel-0.44.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/wheel-0.44.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "site-packages/wheel-0.44.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "3a3c69baae37bab03a835fa8b8a3128f08d69fb513345812beab7c6e5afee041", "sha256_in_prefix": "3a3c69baae37bab03a835fa8b8a3128f08d69fb513345812beab7c6e5afee041", "size_in_bytes": 2313}, {"_path": "site-packages/wheel-0.44.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "13338a2a1feb3a4443a3408edef9760195bdb27eb326bc211a7a7f76f51a982f", "sha256_in_prefix": "13338a2a1feb3a4443a3408edef9760195bdb27eb326bc211a7a7f76f51a982f", "size_in_bytes": 2900}, {"_path": "site-packages/wheel-0.44.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "site-packages/wheel-0.44.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "1b474a6c75845852460e464822eda21682713f4a68534da542077a524a82f9a0", "sha256_in_prefix": "1b474a6c75845852460e464822eda21682713f4a68534da542077a524a82f9a0", "size_in_bytes": 59}, {"_path": "site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "d32e284a36db98350c0e256b84d8cd64e394ed34fce5839b32248aed3f916126", "sha256_in_prefix": "d32e284a36db98350c0e256b84d8cd64e394ed34fce5839b32248aed3f916126", "size_in_bytes": 21496}, {"_path": "site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "f9b5159a79fb4468dc19b8ef9f94284331514e23adf994c5cb86ec23582b0b85", "sha256_in_prefix": "f9b5159a79fb4468dc19b8ef9f94284331514e23adf994c5cb86ec23582b0b85", "size_in_bytes": 376}, {"_path": "site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "c4d9f28a31ebc4dd3acddb5e41f338850d9f48fa20980c0226c0281c8075f412", "sha256_in_prefix": "c4d9f28a31ebc4dd3acddb5e41f338850d9f48fa20980c0226c0281c8075f412", "size_in_bytes": 9512}, {"_path": "site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "sha256_in_prefix": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "size_in_bytes": 621}, {"_path": "site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/__main__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/_bdist_wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/_setuptools_logging.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/bdist_wheel.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/convert.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/pack.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/tags.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/cli/__pycache__/unpack.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/macosx_libfile.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/metadata.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/util.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.12/site-packages/wheel/__pycache__/wheelfile.cpython-312.pyc", "path_type": "pyc_file"}, {"_path": "bin/wheel", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "requested_spec": "None", "sha256": "d828764736babb4322b8102094de38074dedfc71f5ff405c9dfee89191c14ebc", "size": 58585, "subdir": "noarch", "timestamp": 1722797131000, "url": "https://conda.anaconda.org/conda-forge/noarch/wheel-0.44.0-pyhd8ed1ab_0.conda", "version": "0.44.0"}