{"build": "h775f41a_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": [], "extracted_package_dir": "/opt/anaconda3/pkgs/xz-5.2.6-h775f41a_0", "files": ["bin/lzcat", "bin/lzcmp", "bin/lzdiff", "bin/lzegrep", "bin/lzfgrep", "bin/lzgrep", "bin/lzless", "bin/lzma", "bin/lzmadec", "bin/lzmainfo", "bin/lzmore", "bin/unlzma", "bin/unxz", "bin/xz", "bin/xzcat", "bin/xzcmp", "bin/xzdec", "bin/xzdiff", "bin/xzegrep", "bin/xzfgrep", "bin/xzgrep", "bin/xzless", "bin/xzmore", "include/lzma.h", "include/lzma/base.h", "include/lzma/bcj.h", "include/lzma/block.h", "include/lzma/check.h", "include/lzma/container.h", "include/lzma/delta.h", "include/lzma/filter.h", "include/lzma/hardware.h", "include/lzma/index.h", "include/lzma/index_hash.h", "include/lzma/lzma12.h", "include/lzma/stream_flags.h", "include/lzma/version.h", "include/lzma/vli.h", "lib/liblzma.5.dylib", "lib/liblzma.dylib", "lib/pkgconfig/liblzma.pc", "share/doc/xz/AUTHORS", "share/doc/xz/COPYING", "share/doc/xz/COPYING.GPLv2", "share/doc/xz/NEWS", "share/doc/xz/README", "share/doc/xz/THANKS", "share/doc/xz/TODO", "share/doc/xz/examples/00_README.txt", "share/doc/xz/examples/01_compress_easy.c", "share/doc/xz/examples/02_decompress.c", "share/doc/xz/examples/03_compress_custom.c", "share/doc/xz/examples/04_compress_easy_mt.c", "share/doc/xz/examples/Makefile", "share/doc/xz/examples_old/xz_pipe_comp.c", "share/doc/xz/examples_old/xz_pipe_decomp.c", "share/doc/xz/faq.txt", "share/doc/xz/history.txt", "share/doc/xz/lzma-file-format.txt", "share/doc/xz/xz-file-format.txt", "share/man/man1/lzcat.1", "share/man/man1/lzcmp.1", "share/man/man1/lzdiff.1", "share/man/man1/lzegrep.1", "share/man/man1/lzfgrep.1", "share/man/man1/lzgrep.1", "share/man/man1/lzless.1", "share/man/man1/lzma.1", "share/man/man1/lzmadec.1", "share/man/man1/lzmainfo.1", "share/man/man1/lzmore.1", "share/man/man1/unlzma.1", "share/man/man1/unxz.1", "share/man/man1/xz.1", "share/man/man1/xzcat.1", "share/man/man1/xzcmp.1", "share/man/man1/xzdec.1", "share/man/man1/xzdiff.1", "share/man/man1/xzegrep.1", "share/man/man1/xzfgrep.1", "share/man/man1/xzgrep.1", "share/man/man1/xzless.1", "share/man/man1/xzmore.1"], "fn": "xz-5.2.6-h775f41a_0.tar.bz2", "license": "LGPL-2.1 and GPL-2.0", "link": {"source": "/opt/anaconda3/pkgs/xz-5.2.6-h775f41a_0", "type": 1}, "md5": "a72f9d4ea13d55d745ff1ed594747f10", "name": "xz", "package_tarball_full_path": "/opt/anaconda3/pkgs/xz-5.2.6-h775f41a_0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/lzcat", "path_type": "softlink", "sha256": "2ca0e524110baf2c47b41d60dc2acd49b06672db116a821317968e8d0bde279c", "size_in_bytes": 74656}, {"_path": "bin/lzcmp", "path_type": "softlink", "sha256": "b6526d09d24c97ac9cc7b046efc9056eb904410dbe1056928fc7dafbf179e832", "size_in_bytes": 7307}, {"_path": "bin/lzdiff", "path_type": "softlink", "sha256": "b6526d09d24c97ac9cc7b046efc9056eb904410dbe1056928fc7dafbf179e832", "size_in_bytes": 7307}, {"_path": "bin/lzegrep", "path_type": "softlink", "sha256": "d2cf18d6e564b8ddf2b6bb01f971d030e4bf35ac1855fc1d29e756a137ebc6cd", "size_in_bytes": 10293}, {"_path": "bin/lzfgrep", "path_type": "softlink", "sha256": "d2cf18d6e564b8ddf2b6bb01f971d030e4bf35ac1855fc1d29e756a137ebc6cd", "size_in_bytes": 10293}, {"_path": "bin/lzgrep", "path_type": "softlink", "sha256": "d2cf18d6e564b8ddf2b6bb01f971d030e4bf35ac1855fc1d29e756a137ebc6cd", "size_in_bytes": 10293}, {"_path": "bin/lzless", "path_type": "softlink", "sha256": "02c071c8c565ca6e0ee6ff22d1737ec56248274f82d248fb31a1396573168be3", "size_in_bytes": 1821}, {"_path": "bin/lzma", "path_type": "softlink", "sha256": "2ca0e524110baf2c47b41d60dc2acd49b06672db116a821317968e8d0bde279c", "size_in_bytes": 74656}, {"_path": "bin/lzmadec", "path_type": "hardlink", "sha256": "358551fb09319b04b98a1b2d7a73b94cb9b0ba911e8e34539698c80bd59f1855", "sha256_in_prefix": "358551fb09319b04b98a1b2d7a73b94cb9b0ba911e8e34539698c80bd59f1855", "size_in_bytes": 14016}, {"_path": "bin/lzmainfo", "path_type": "hardlink", "sha256": "728ffebe7a879b4facf122971866dc25e2b54a4b3864ac0e69a2ef54e0d3ddaa", "sha256_in_prefix": "728ffebe7a879b4facf122971866dc25e2b54a4b3864ac0e69a2ef54e0d3ddaa", "size_in_bytes": 13776}, {"_path": "bin/lzmore", "path_type": "softlink", "sha256": "1eb23d10501ef995cecabf23d22d52c276425dd455cf08fd66b9cc9172df4f75", "size_in_bytes": 2198}, {"_path": "bin/unlzma", "path_type": "softlink", "sha256": "2ca0e524110baf2c47b41d60dc2acd49b06672db116a821317968e8d0bde279c", "size_in_bytes": 74656}, {"_path": "bin/unxz", "path_type": "softlink", "sha256": "2ca0e524110baf2c47b41d60dc2acd49b06672db116a821317968e8d0bde279c", "size_in_bytes": 74656}, {"_path": "bin/xz", "path_type": "hardlink", "sha256": "2ca0e524110baf2c47b41d60dc2acd49b06672db116a821317968e8d0bde279c", "sha256_in_prefix": "2ca0e524110baf2c47b41d60dc2acd49b06672db116a821317968e8d0bde279c", "size_in_bytes": 74656}, {"_path": "bin/xzcat", "path_type": "softlink", "sha256": "2ca0e524110baf2c47b41d60dc2acd49b06672db116a821317968e8d0bde279c", "size_in_bytes": 74656}, {"_path": "bin/xzcmp", "path_type": "softlink", "sha256": "b6526d09d24c97ac9cc7b046efc9056eb904410dbe1056928fc7dafbf179e832", "size_in_bytes": 7307}, {"_path": "bin/xzdec", "path_type": "hardlink", "sha256": "136df45de3dde0fce9278072fcde289ed52e61c5eca1adbae8a63cb670b5a233", "sha256_in_prefix": "136df45de3dde0fce9278072fcde289ed52e61c5eca1adbae8a63cb670b5a233", "size_in_bytes": 14016}, {"_path": "bin/xzdiff", "path_type": "hardlink", "sha256": "b6526d09d24c97ac9cc7b046efc9056eb904410dbe1056928fc7dafbf179e832", "sha256_in_prefix": "b6526d09d24c97ac9cc7b046efc9056eb904410dbe1056928fc7dafbf179e832", "size_in_bytes": 7307}, {"_path": "bin/xzegrep", "path_type": "softlink", "sha256": "d2cf18d6e564b8ddf2b6bb01f971d030e4bf35ac1855fc1d29e756a137ebc6cd", "size_in_bytes": 10293}, {"_path": "bin/xzfgrep", "path_type": "softlink", "sha256": "d2cf18d6e564b8ddf2b6bb01f971d030e4bf35ac1855fc1d29e756a137ebc6cd", "size_in_bytes": 10293}, {"_path": "bin/xzgrep", "path_type": "hardlink", "sha256": "d2cf18d6e564b8ddf2b6bb01f971d030e4bf35ac1855fc1d29e756a137ebc6cd", "sha256_in_prefix": "d2cf18d6e564b8ddf2b6bb01f971d030e4bf35ac1855fc1d29e756a137ebc6cd", "size_in_bytes": 10293}, {"_path": "bin/xzless", "path_type": "hardlink", "sha256": "02c071c8c565ca6e0ee6ff22d1737ec56248274f82d248fb31a1396573168be3", "sha256_in_prefix": "02c071c8c565ca6e0ee6ff22d1737ec56248274f82d248fb31a1396573168be3", "size_in_bytes": 1821}, {"_path": "bin/xzmore", "path_type": "hardlink", "sha256": "1eb23d10501ef995cecabf23d22d52c276425dd455cf08fd66b9cc9172df4f75", "sha256_in_prefix": "1eb23d10501ef995cecabf23d22d52c276425dd455cf08fd66b9cc9172df4f75", "size_in_bytes": 2198}, {"_path": "include/lzma.h", "path_type": "hardlink", "sha256": "d831a8daf0b288b4bc512ba09eef2d8a6c519f1be679ea1d6df7483726376070", "sha256_in_prefix": "d831a8daf0b288b4bc512ba09eef2d8a6c519f1be679ea1d6df7483726376070", "size_in_bytes": 9922}, {"_path": "include/lzma/base.h", "path_type": "hardlink", "sha256": "2e4b5ffe0e729ef43c35600936a570b3d7e692fdbeaf8c887646c87de7aef31a", "sha256_in_prefix": "2e4b5ffe0e729ef43c35600936a570b3d7e692fdbeaf8c887646c87de7aef31a", "size_in_bytes": 24859}, {"_path": "include/lzma/bcj.h", "path_type": "hardlink", "sha256": "485ee1ac185747b6e5324094aa462af194ba3a22a0206314e25f70423045e43d", "sha256_in_prefix": "485ee1ac185747b6e5324094aa462af194ba3a22a0206314e25f70423045e43d", "size_in_bytes": 2630}, {"_path": "include/lzma/block.h", "path_type": "hardlink", "sha256": "d62b1e56044a7be0991f1c421c8da1b3dba4b4b347583eac7d8a0f87300e3058", "sha256_in_prefix": "d62b1e56044a7be0991f1c421c8da1b3dba4b4b347583eac7d8a0f87300e3058", "size_in_bytes": 21929}, {"_path": "include/lzma/check.h", "path_type": "hardlink", "sha256": "79ef75b06fe389ccbc47ebeea1bb704157a58fe9710ddfbac8a62035359f9ae1", "sha256_in_prefix": "79ef75b06fe389ccbc47ebeea1bb704157a58fe9710ddfbac8a62035359f9ae1", "size_in_bytes": 4255}, {"_path": "include/lzma/container.h", "path_type": "hardlink", "sha256": "13fbba65515bed9d108e97cba3227604291545290fec3f11d9f5babcc6811404", "sha256_in_prefix": "13fbba65515bed9d108e97cba3227604291545290fec3f11d9f5babcc6811404", "size_in_bytes": 24844}, {"_path": "include/lzma/delta.h", "path_type": "hardlink", "sha256": "db9db049ab07363921bf19320174afbab16a1b4d401f797a5b2232dcb89b9d64", "sha256_in_prefix": "db9db049ab07363921bf19320174afbab16a1b4d401f797a5b2232dcb89b9d64", "size_in_bytes": 1865}, {"_path": "include/lzma/filter.h", "path_type": "hardlink", "sha256": "0c30f1e1271e4bd06e07934b31b76edddbb7d8616e2b8043b36771ade8eb294b", "sha256_in_prefix": "0c30f1e1271e4bd06e07934b31b76edddbb7d8616e2b8043b36771ade8eb294b", "size_in_bytes": 16520}, {"_path": "include/lzma/hardware.h", "path_type": "hardlink", "sha256": "7c9c7fdd29650a730e59281ea38e3826d94b518fa7e23573b9303ac8f3421083", "sha256_in_prefix": "7c9c7fdd29650a730e59281ea38e3826d94b518fa7e23573b9303ac8f3421083", "size_in_bytes": 2604}, {"_path": "include/lzma/index.h", "path_type": "hardlink", "sha256": "9eb7451f4d8de7d51a17585b7a86c3b4eb02d00d7e7fc1c390255e34231f3516", "sha256_in_prefix": "9eb7451f4d8de7d51a17585b7a86c3b4eb02d00d7e7fc1c390255e34231f3516", "size_in_bytes": 23491}, {"_path": "include/lzma/index_hash.h", "path_type": "hardlink", "sha256": "0840c2ae8dedc05a7ffe1597ead131532a8dc03521728d1d38e55da0fa769831", "sha256_in_prefix": "0840c2ae8dedc05a7ffe1597ead131532a8dc03521728d1d38e55da0fa769831", "size_in_bytes": 3914}, {"_path": "include/lzma/lzma12.h", "path_type": "hardlink", "sha256": "caf8948b9306d508026cc3bbadea579eb8e75a24c444fdbe9986a4cc01a7b362", "sha256_in_prefix": "caf8948b9306d508026cc3bbadea579eb8e75a24c444fdbe9986a4cc01a7b362", "size_in_bytes": 14744}, {"_path": "include/lzma/stream_flags.h", "path_type": "hardlink", "sha256": "beba70fa9d83dc6a7fcfae9b1f8d07b3b5acbbdc789f008e63da4206e2434acc", "sha256_in_prefix": "beba70fa9d83dc6a7fcfae9b1f8d07b3b5acbbdc789f008e63da4206e2434acc", "size_in_bytes": 8253}, {"_path": "include/lzma/version.h", "path_type": "hardlink", "sha256": "83038dff2305cfc1a7baf1330fc7c2b47de793d6347a3b1e433988c4db86e57c", "sha256_in_prefix": "83038dff2305cfc1a7baf1330fc7c2b47de793d6347a3b1e433988c4db86e57c", "size_in_bytes": 3497}, {"_path": "include/lzma/vli.h", "path_type": "hardlink", "sha256": "1efbbbfc93d109aff5bc6ec025437bbf8b99410975d97244781e9a163fe50f4d", "sha256_in_prefix": "1efbbbfc93d109aff5bc6ec025437bbf8b99410975d97244781e9a163fe50f4d", "size_in_bytes": 6622}, {"_path": "lib/liblzma.5.dylib", "path_type": "hardlink", "sha256": "fc76ee0c57b75cce2b03c828cd2aed31a08db02f8fe5376c8ab0f74879b67bb1", "sha256_in_prefix": "fc76ee0c57b75cce2b03c828cd2aed31a08db02f8fe5376c8ab0f74879b67bb1", "size_in_bytes": 158544}, {"_path": "lib/liblzma.dylib", "path_type": "softlink", "sha256": "fc76ee0c57b75cce2b03c828cd2aed31a08db02f8fe5376c8ab0f74879b67bb1", "size_in_bytes": 158544}, {"_path": "lib/pkgconfig/liblzma.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/xz_1660346701016/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "378dd93438a976cfbadc79d72ccfec53fcde37f1a9a3235048eef9b59aac0498", "sha256_in_prefix": "089aeba8a51f7743edf1a7b04a4d9651ecd092a8745f16c858f77956d1fb1678", "size_in_bytes": 1394}, {"_path": "share/doc/xz/AUTHORS", "path_type": "hardlink", "sha256": "c07ea89b85db044f5e4a28fcc18a1dd900541737fb5ef8a69df1e1eb603f5966", "sha256_in_prefix": "c07ea89b85db044f5e4a28fcc18a1dd900541737fb5ef8a69df1e1eb603f5966", "size_in_bytes": 1076}, {"_path": "share/doc/xz/COPYING", "path_type": "hardlink", "sha256": "bcb02973ef6e87ea73d331b3a80df7748407f17efdb784b61b47e0e610d3bb5c", "sha256_in_prefix": "bcb02973ef6e87ea73d331b3a80df7748407f17efdb784b61b47e0e610d3bb5c", "size_in_bytes": 2775}, {"_path": "share/doc/xz/COPYING.GPLv2", "path_type": "hardlink", "sha256": "8177f97513213526df2cf6184d8ff986c675afb514d4e68a404010521b880643", "sha256_in_prefix": "8177f97513213526df2cf6184d8ff986c675afb514d4e68a404010521b880643", "size_in_bytes": 18092}, {"_path": "share/doc/xz/NEWS", "path_type": "hardlink", "sha256": "0cb884673243bafca71dc7ec6608e558a93ac711f65205159215c42c0bcd2565", "sha256_in_prefix": "0cb884673243bafca71dc7ec6608e558a93ac711f65205159215c42c0bcd2565", "size_in_bytes": 35423}, {"_path": "share/doc/xz/README", "path_type": "hardlink", "sha256": "ecd806b395d20f9f4c0eea86ee9927bb986b75e8066cc92e145c629c367e9cc8", "sha256_in_prefix": "ecd806b395d20f9f4c0eea86ee9927bb986b75e8066cc92e145c629c367e9cc8", "size_in_bytes": 9936}, {"_path": "share/doc/xz/THANKS", "path_type": "hardlink", "sha256": "56bc8e54423b25587468618a31a95109adba8b98021d8247960ad98c6210bad7", "sha256_in_prefix": "56bc8e54423b25587468618a31a95109adba8b98021d8247960ad98c6210bad7", "size_in_bytes": 2916}, {"_path": "share/doc/xz/TODO", "path_type": "hardlink", "sha256": "2ce425810436c5299aa5e4a8b0186c0de15a496697b518481f606abb421e80a9", "sha256_in_prefix": "2ce425810436c5299aa5e4a8b0186c0de15a496697b518481f606abb421e80a9", "size_in_bytes": 4040}, {"_path": "share/doc/xz/examples/00_README.txt", "path_type": "hardlink", "sha256": "f0ddaa731c89d6028f55281229e56b89f32b8c477aba4f52367488f0f42651be", "sha256_in_prefix": "f0ddaa731c89d6028f55281229e56b89f32b8c477aba4f52367488f0f42651be", "size_in_bytes": 1037}, {"_path": "share/doc/xz/examples/01_compress_easy.c", "path_type": "hardlink", "sha256": "913af652f6eac0c728762ce5537d3ea175538573df6f34358ce522fc6087c40a", "sha256_in_prefix": "913af652f6eac0c728762ce5537d3ea175538573df6f34358ce522fc6087c40a", "size_in_bytes": 9533}, {"_path": "share/doc/xz/examples/02_decompress.c", "path_type": "hardlink", "sha256": "1c8733c08e1edbd727bb623eb23b5505b32a4306e310ee4f9048fc9bf4af8de2", "sha256_in_prefix": "1c8733c08e1edbd727bb623eb23b5505b32a4306e310ee4f9048fc9bf4af8de2", "size_in_bytes": 8913}, {"_path": "share/doc/xz/examples/03_compress_custom.c", "path_type": "hardlink", "sha256": "914afd1e3494d9942ef752123f9743fa9427d5a82ca3e593794b9a4d9e390f42", "sha256_in_prefix": "914afd1e3494d9942ef752123f9743fa9427d5a82ca3e593794b9a4d9e390f42", "size_in_bytes": 5025}, {"_path": "share/doc/xz/examples/04_compress_easy_mt.c", "path_type": "hardlink", "sha256": "80a5d7e1acd455ffb55bd1ca26f767789171293a231e6645ca991b83b954988c", "sha256_in_prefix": "80a5d7e1acd455ffb55bd1ca26f767789171293a231e6645ca991b83b954988c", "size_in_bytes": 5214}, {"_path": "share/doc/xz/examples/Makefile", "path_type": "hardlink", "sha256": "067ac8dbf5a9cab8c2a12b3fadda34c93656308f150a8a195bfcdb071ca043a7", "sha256_in_prefix": "067ac8dbf5a9cab8c2a12b3fadda34c93656308f150a8a195bfcdb071ca043a7", "size_in_bytes": 337}, {"_path": "share/doc/xz/examples_old/xz_pipe_comp.c", "path_type": "hardlink", "sha256": "fce7eefb9149c5f5a43869e07a4a576c1f2af4ca0aae6872bd7ca50ed8c85522", "sha256_in_prefix": "fce7eefb9149c5f5a43869e07a4a576c1f2af4ca0aae6872bd7ca50ed8c85522", "size_in_bytes": 3043}, {"_path": "share/doc/xz/examples_old/xz_pipe_decomp.c", "path_type": "hardlink", "sha256": "5d157c3c397fffc3b0489e49ef1d396fcfe6153f134ec5ea44ef0acc7fe474aa", "sha256_in_prefix": "5d157c3c397fffc3b0489e49ef1d396fcfe6153f134ec5ea44ef0acc7fe474aa", "size_in_bytes": 3130}, {"_path": "share/doc/xz/faq.txt", "path_type": "hardlink", "sha256": "eff832647a62f3b582e0255a8d450523074874d16bf3bdcbae76acbfe23fbb29", "sha256_in_prefix": "eff832647a62f3b582e0255a8d450523074874d16bf3bdcbae76acbfe23fbb29", "size_in_bytes": 9411}, {"_path": "share/doc/xz/history.txt", "path_type": "hardlink", "sha256": "9d6a0a72822734a0afb1816e07f0a7edab03339119bed4f393c1c7eec884eab6", "sha256_in_prefix": "9d6a0a72822734a0afb1816e07f0a7edab03339119bed4f393c1c7eec884eab6", "size_in_bytes": 7427}, {"_path": "share/doc/xz/lzma-file-format.txt", "path_type": "hardlink", "sha256": "beda7e9710f03003286d2e99c159e16e727e286e22da6a9bb311e493b80e2725", "sha256_in_prefix": "beda7e9710f03003286d2e99c159e16e727e286e22da6a9bb311e493b80e2725", "size_in_bytes": 6081}, {"_path": "share/doc/xz/xz-file-format.txt", "path_type": "hardlink", "sha256": "fada567e0ebd8b910d2c3210d13e74f3fcc8475d64e29e35db0fc05e3c6820f5", "sha256_in_prefix": "fada567e0ebd8b910d2c3210d13e74f3fcc8475d64e29e35db0fc05e3c6820f5", "size_in_bytes": 43305}, {"_path": "share/man/man1/lzcat.1", "path_type": "softlink", "sha256": "f30f1ef1eb2248ffc6ddc9a3eabab159879b02d237329ec64279c1ad4ba18319", "size_in_bytes": 65586}, {"_path": "share/man/man1/lzcmp.1", "path_type": "softlink", "sha256": "92d4741fa9175236861130b25b4aaf827c28c9e96dabc93bfc0cf3f47f799504", "size_in_bytes": 1556}, {"_path": "share/man/man1/lzdiff.1", "path_type": "softlink", "sha256": "92d4741fa9175236861130b25b4aaf827c28c9e96dabc93bfc0cf3f47f799504", "size_in_bytes": 1556}, {"_path": "share/man/man1/lzegrep.1", "path_type": "softlink", "sha256": "25dbf88e1daaf43e360f27f45df7fe0110748bef0ee5b00c5ec6b9d0a0a6b38e", "size_in_bytes": 1777}, {"_path": "share/man/man1/lzfgrep.1", "path_type": "softlink", "sha256": "25dbf88e1daaf43e360f27f45df7fe0110748bef0ee5b00c5ec6b9d0a0a6b38e", "size_in_bytes": 1777}, {"_path": "share/man/man1/lzgrep.1", "path_type": "softlink", "sha256": "25dbf88e1daaf43e360f27f45df7fe0110748bef0ee5b00c5ec6b9d0a0a6b38e", "size_in_bytes": 1777}, {"_path": "share/man/man1/lzless.1", "path_type": "softlink", "sha256": "2db6570b6f62b6f0d46fecfc18ead93000abaec97399514b31e18edb7ab2fecb", "size_in_bytes": 1360}, {"_path": "share/man/man1/lzma.1", "path_type": "softlink", "sha256": "f30f1ef1eb2248ffc6ddc9a3eabab159879b02d237329ec64279c1ad4ba18319", "size_in_bytes": 65586}, {"_path": "share/man/man1/lzmadec.1", "path_type": "softlink", "sha256": "20e56b65af31a9488483f01659c681de022da370d36a427b232246e4eb39bb6f", "size_in_bytes": 2838}, {"_path": "share/man/man1/lzmainfo.1", "path_type": "hardlink", "sha256": "0963a1fe3e0539f036aaa9adf5bb179df10f2abe5f7f470c87340a5619e5f500", "sha256_in_prefix": "0963a1fe3e0539f036aaa9adf5bb179df10f2abe5f7f470c87340a5619e5f500", "size_in_bytes": 1250}, {"_path": "share/man/man1/lzmore.1", "path_type": "softlink", "sha256": "551a2a7f6e2e5626b0cee4580a0107d81410afd742da25001c846b4fa7645b07", "size_in_bytes": 1153}, {"_path": "share/man/man1/unlzma.1", "path_type": "softlink", "sha256": "f30f1ef1eb2248ffc6ddc9a3eabab159879b02d237329ec64279c1ad4ba18319", "size_in_bytes": 65586}, {"_path": "share/man/man1/unxz.1", "path_type": "softlink", "sha256": "f30f1ef1eb2248ffc6ddc9a3eabab159879b02d237329ec64279c1ad4ba18319", "size_in_bytes": 65586}, {"_path": "share/man/man1/xz.1", "path_type": "hardlink", "sha256": "f30f1ef1eb2248ffc6ddc9a3eabab159879b02d237329ec64279c1ad4ba18319", "sha256_in_prefix": "f30f1ef1eb2248ffc6ddc9a3eabab159879b02d237329ec64279c1ad4ba18319", "size_in_bytes": 65586}, {"_path": "share/man/man1/xzcat.1", "path_type": "softlink", "sha256": "f30f1ef1eb2248ffc6ddc9a3eabab159879b02d237329ec64279c1ad4ba18319", "size_in_bytes": 65586}, {"_path": "share/man/man1/xzcmp.1", "path_type": "softlink", "sha256": "92d4741fa9175236861130b25b4aaf827c28c9e96dabc93bfc0cf3f47f799504", "size_in_bytes": 1556}, {"_path": "share/man/man1/xzdec.1", "path_type": "hardlink", "sha256": "20e56b65af31a9488483f01659c681de022da370d36a427b232246e4eb39bb6f", "sha256_in_prefix": "20e56b65af31a9488483f01659c681de022da370d36a427b232246e4eb39bb6f", "size_in_bytes": 2838}, {"_path": "share/man/man1/xzdiff.1", "path_type": "hardlink", "sha256": "92d4741fa9175236861130b25b4aaf827c28c9e96dabc93bfc0cf3f47f799504", "sha256_in_prefix": "92d4741fa9175236861130b25b4aaf827c28c9e96dabc93bfc0cf3f47f799504", "size_in_bytes": 1556}, {"_path": "share/man/man1/xzegrep.1", "path_type": "softlink", "sha256": "25dbf88e1daaf43e360f27f45df7fe0110748bef0ee5b00c5ec6b9d0a0a6b38e", "size_in_bytes": 1777}, {"_path": "share/man/man1/xzfgrep.1", "path_type": "softlink", "sha256": "25dbf88e1daaf43e360f27f45df7fe0110748bef0ee5b00c5ec6b9d0a0a6b38e", "size_in_bytes": 1777}, {"_path": "share/man/man1/xzgrep.1", "path_type": "hardlink", "sha256": "25dbf88e1daaf43e360f27f45df7fe0110748bef0ee5b00c5ec6b9d0a0a6b38e", "sha256_in_prefix": "25dbf88e1daaf43e360f27f45df7fe0110748bef0ee5b00c5ec6b9d0a0a6b38e", "size_in_bytes": 1777}, {"_path": "share/man/man1/xzless.1", "path_type": "hardlink", "sha256": "2db6570b6f62b6f0d46fecfc18ead93000abaec97399514b31e18edb7ab2fecb", "sha256_in_prefix": "2db6570b6f62b6f0d46fecfc18ead93000abaec97399514b31e18edb7ab2fecb", "size_in_bytes": 1360}, {"_path": "share/man/man1/xzmore.1", "path_type": "hardlink", "sha256": "551a2a7f6e2e5626b0cee4580a0107d81410afd742da25001c846b4fa7645b07", "sha256_in_prefix": "551a2a7f6e2e5626b0cee4580a0107d81410afd742da25001c846b4fa7645b07", "size_in_bytes": 1153}], "paths_version": 1}, "requested_spec": "None", "sha256": "eb09823f34cc2dd663c0ec4ab13f246f45dcd52e5b8c47b9864361de5204a1c8", "size": 238119, "subdir": "osx-64", "timestamp": 1660346964000, "url": "https://conda.anaconda.org/conda-forge/osx-64/xz-5.2.6-h775f41a_0.tar.bz2", "version": "5.2.6"}