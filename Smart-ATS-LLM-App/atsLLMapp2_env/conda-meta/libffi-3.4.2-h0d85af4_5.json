{"build": "h0d85af4_5", "build_number": 5, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": [], "extracted_package_dir": "/opt/anaconda3/pkgs/libffi-3.4.2-h0d85af4_5", "files": ["include/ffi.h", "include/ffitarget.h", "lib/libffi.8.dylib", "lib/libffi.a", "lib/libffi.dylib", "lib/pkgconfig/libffi.pc", "share/info/libffi.info", "share/man/man3/ffi.3", "share/man/man3/ffi_call.3", "share/man/man3/ffi_prep_cif.3", "share/man/man3/ffi_prep_cif_var.3"], "fn": "libffi-3.4.2-h0d85af4_5.tar.bz2", "license": "MIT", "link": {"source": "/opt/anaconda3/pkgs/libffi-3.4.2-h0d85af4_5", "type": 1}, "md5": "ccb34fb14960ad8b125962d3d79b31a9", "name": "libffi", "package_tarball_full_path": "/opt/anaconda3/pkgs/libffi-3.4.2-h0d85af4_5.tar.bz2", "paths_data": {"paths": [{"_path": "include/ffi.h", "path_type": "hardlink", "sha256": "940007c4c664405f1c89bb3cd3b81d2a1bc3da68946c01cda448a6f3b13ec394", "sha256_in_prefix": "940007c4c664405f1c89bb3cd3b81d2a1bc3da68946c01cda448a6f3b13ec394", "size_in_bytes": 14411}, {"_path": "include/ffitarget.h", "path_type": "hardlink", "sha256": "c83585125f3b244a1fe4551366f464734b97c11139ce2e2b5b4141183ba7ebb2", "sha256_in_prefix": "c83585125f3b244a1fe4551366f464734b97c11139ce2e2b5b4141183ba7ebb2", "size_in_bytes": 4741}, {"_path": "lib/libffi.8.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libffi_1636488228368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pl", "sha256": "61879d1600ba8a73bab546c9268b56b79324d4276c5c6e5dd0a6e8e2f23fca6e", "sha256_in_prefix": "053a9d16c70c12e0a32ab1fa219ccaf8dbbfaaeb8d86bad2c9b0d0c542ea6877", "size_in_bytes": 38364}, {"_path": "lib/libffi.a", "path_type": "hardlink", "sha256": "255428d15a43586990ab9efc8150a4b9e42d127c6eda722f91943774cd4f24fa", "sha256_in_prefix": "255428d15a43586990ab9efc8150a4b9e42d127c6eda722f91943774cd4f24fa", "size_in_bytes": 43688}, {"_path": "lib/libffi.dylib", "path_type": "softlink", "sha256": "61879d1600ba8a73bab546c9268b56b79324d4276c5c6e5dd0a6e8e2f23fca6e", "size_in_bytes": 38364}, {"_path": "lib/pkgconfig/libffi.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libffi_1636488228368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pl", "sha256": "b3d9cb7ef3f720f3327a0ebe13374054fd6248d9ecbb4f401495538368b966a7", "sha256_in_prefix": "817e1450594aa96961186541f5aefa7c0026d438d91a62cc59efeb7566c5d7a9", "size_in_bytes": 756}, {"_path": "share/info/libffi.info", "path_type": "hardlink", "sha256": "59d293758be54f369906d16de77f96aea2be15f1bfb690016068f2f42e4eb445", "sha256_in_prefix": "59d293758be54f369906d16de77f96aea2be15f1bfb690016068f2f42e4eb445", "size_in_bytes": 38369}, {"_path": "share/man/man3/ffi.3", "path_type": "hardlink", "sha256": "aa4730e114c305943a2226a524ed8447dc6b66a184523999868e5433c2c9de74", "sha256_in_prefix": "aa4730e114c305943a2226a524ed8447dc6b66a184523999868e5433c2c9de74", "size_in_bytes": 850}, {"_path": "share/man/man3/ffi_call.3", "path_type": "hardlink", "sha256": "2817ce7b78cb737d7b85b18b45899470f5f565f990d056d3d8cfabf6d779477f", "sha256_in_prefix": "2817ce7b78cb737d7b85b18b45899470f5f565f990d056d3d8cfabf6d779477f", "size_in_bytes": 2333}, {"_path": "share/man/man3/ffi_prep_cif.3", "path_type": "hardlink", "sha256": "f60c5bb9d04b55988da13511a2c3edfa0f39fb6f51abfb8ac24d0b161c4169c0", "sha256_in_prefix": "f60c5bb9d04b55988da13511a2c3edfa0f39fb6f51abfb8ac24d0b161c4169c0", "size_in_bytes": 1158}, {"_path": "share/man/man3/ffi_prep_cif_var.3", "path_type": "hardlink", "sha256": "9365685252f33f13627c9303bc01883b764227132069260c19e94100ff442a51", "sha256_in_prefix": "9365685252f33f13627c9303bc01883b764227132069260c19e94100ff442a51", "size_in_bytes": 1321}], "paths_version": 1}, "requested_spec": "None", "sha256": "7a2d27a936ceee6942ea4d397f9c7d136f12549d86f7617e8b6bad51e01a941f", "size": 51348, "subdir": "osx-64", "timestamp": 1636488394000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.2-h0d85af4_5.tar.bz2", "version": "3.4.2"}