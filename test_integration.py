#!/usr/bin/env python3
"""
Integration test script for Smart ATS Frontend-Backend communication
"""
import requests
import json
import time
import os
from pathlib import Path

# Configuration
BACKEND_URL = "https://api-mysmartats.onrender.com"
LOCAL_BACKEND_URL = "http://localhost:5000"

def test_backend_health(url):
    """Test if backend is accessible"""
    try:
        print(f"Testing backend health at: {url}")
        response = requests.get(f"{url}/", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend is healthy!")
            print(f"   Status: {data.get('status')}")
            print(f"   Message: {data.get('message')}")
            print(f"   Version: {data.get('version')}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend not accessible: {e}")
        return False

def test_cors_headers(url):
    """Test CORS configuration"""
    try:
        print(f"\nTesting CORS headers...")
        response = requests.options(f"{url}/analyze", headers={
            'Origin': 'http://localhost:5173',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        })
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        print(f"✅ CORS Headers:")
        for header, value in cors_headers.items():
            print(f"   {header}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ CORS test failed: {e}")
        return False

def create_sample_pdf():
    """Create a simple PDF for testing (requires reportlab)"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        import io
        
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        
        # Add sample resume content
        p.drawString(100, 750, "John Doe")
        p.drawString(100, 730, "Software Developer")
        p.drawString(100, 710, "")
        p.drawString(100, 690, "Skills:")
        p.drawString(120, 670, "- Python programming")
        p.drawString(120, 650, "- Web development")
        p.drawString(120, 630, "- Database management")
        p.drawString(120, 610, "- API development")
        p.drawString(100, 590, "")
        p.drawString(100, 570, "Experience:")
        p.drawString(120, 550, "- 3 years as Python Developer")
        p.drawString(120, 530, "- Built REST APIs using Flask")
        p.drawString(120, 510, "- Worked with PostgreSQL databases")
        
        p.save()
        buffer.seek(0)
        return buffer
        
    except ImportError:
        print("⚠️  reportlab not installed. Cannot create sample PDF.")
        print("   Install with: pip install reportlab")
        return None

def test_analyze_endpoint(url):
    """Test the analyze endpoint with sample data"""
    try:
        print(f"\nTesting analyze endpoint...")
        
        # Sample job description
        job_description = """
        We are looking for a Senior Python Developer with the following qualifications:
        
        Required Skills:
        - 3+ years of Python programming experience
        - Experience with Flask or Django frameworks
        - REST API development and integration
        - Database design and management (PostgreSQL, MySQL)
        - Git version control
        - Docker containerization
        - Unit testing and test-driven development
        
        Preferred Skills:
        - Cloud platforms (AWS, GCP, Azure)
        - Microservices architecture
        - CI/CD pipelines
        - Kubernetes orchestration
        - Machine learning libraries (scikit-learn, pandas)
        
        Responsibilities:
        - Design and develop scalable web applications
        - Build and maintain REST APIs
        - Collaborate with cross-functional teams
        - Write clean, maintainable code
        - Participate in code reviews
        """
        
        # Try to create a sample PDF
        pdf_buffer = create_sample_pdf()
        
        if pdf_buffer is None:
            print("❌ Cannot test analyze endpoint without PDF file")
            print("   To test manually:")
            print(f"   1. POST to {url}/analyze")
            print("   2. Form data: job_description (text) and resume (PDF file)")
            return False
        
        # Prepare the request
        files = {
            'resume': ('sample_resume.pdf', pdf_buffer, 'application/pdf')
        }
        data = {
            'job_description': job_description
        }
        
        print("   Sending request to analyze endpoint...")
        response = requests.post(f"{url}/analyze", files=files, data=data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Analyze endpoint working!")
            print(f"   JD Match: {result.get('jd_match', 'N/A')}")
            print(f"   Missing Keywords: {len(result.get('missing_keywords', []))} found")
            print(f"   Profile Summary: {len(result.get('profile_summary', ''))} characters")
            return True
        else:
            print(f"❌ Analyze endpoint failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Analyze endpoint test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Smart ATS Integration Test")
    print("=" * 50)
    
    # Test both local and deployed backend
    backends_to_test = [
        ("Deployed Backend", BACKEND_URL),
        ("Local Backend", LOCAL_BACKEND_URL)
    ]
    
    results = {}
    
    for name, url in backends_to_test:
        print(f"\n📡 Testing {name}")
        print("-" * 30)
        
        # Test health
        health_ok = test_backend_health(url)
        
        if health_ok:
            # Test CORS
            cors_ok = test_cors_headers(url)
            
            # Test analyze endpoint
            analyze_ok = test_analyze_endpoint(url)
            
            results[name] = {
                'health': health_ok,
                'cors': cors_ok,
                'analyze': analyze_ok,
                'overall': health_ok and cors_ok and analyze_ok
            }
        else:
            results[name] = {
                'health': False,
                'cors': False,
                'analyze': False,
                'overall': False
            }
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    for name, result in results.items():
        status = "✅ PASS" if result['overall'] else "❌ FAIL"
        print(f"{name}: {status}")
        if not result['overall']:
            print(f"   Health: {'✅' if result['health'] else '❌'}")
            print(f"   CORS: {'✅' if result['cors'] else '❌'}")
            print(f"   Analyze: {'✅' if result['analyze'] else '❌'}")
    
    # Frontend testing instructions
    print("\n🌐 FRONTEND TESTING")
    print("-" * 30)
    print("To test the frontend:")
    print("1. cd Smart_ATS")
    print("2. npm run dev")
    print("3. Open http://localhost:5173")
    print("4. Upload a PDF resume and enter a job description")
    print("5. Click 'Analyze Resume'")
    
    # Check if any backend is working
    working_backends = [name for name, result in results.items() if result['overall']]
    
    if working_backends:
        print(f"\n✅ Integration ready! Working backends: {', '.join(working_backends)}")
    else:
        print(f"\n❌ No working backends found. Check deployment and configuration.")

if __name__ == "__main__":
    main()
